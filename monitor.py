import asyncio
import logging
import os
import time
import json
import random
import shutil
from datetime import datetime, timedelta
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, Error as PlaywrightError
from agent_agno import is_bn_alpha_related
from feishu_notify import send_feishu_message, send_startup_test_message, send_alpha_notification_with_screenshot
from utils import load_sent_ids, save_sent_id
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger

# 配置日志记录
LOG_DIR = "logs"
LOG_FILE = os.path.join(LOG_DIR, f"monitor_{datetime.now().strftime('%Y%m%d')}.log")
os.makedirs(LOG_DIR, exist_ok=True)

# 设置日志处理器
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 文件处理器
file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
logger.addHandler(file_handler)

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)

BASE_URL = "https://www.binance.com"
ANNOUNCEMENT_LIST = "https://www.binance.com/zh-CN/support/announcement/list/48"

# 数据存储目录
DATA_DIR = "data"
ANNOUNCEMENT_DATA_DIR = os.path.join(DATA_DIR, "announcements")
SCREENSHOTS_DIR = "screenshots"
os.makedirs(ANNOUNCEMENT_DATA_DIR, exist_ok=True)
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# 数据保留天数
DATA_RETENTION_DAYS = 7

def exponential_backoff(attempt, max_delay=60):
    """计算指数退避延迟时间，包含随机抖动"""
    base_delay = min(2 ** attempt, max_delay)
    jitter = random.uniform(0, 0.1 * base_delay)
    return base_delay + jitter

def cleanup_old_data():
    """清理旧数据和截图"""
    logging.info("🧹 开始清理旧数据和截图...")
    cutoff_time = datetime.now() - timedelta(days=DATA_RETENTION_DAYS)
    
    # 清理公告数据
    for filename in os.listdir(ANNOUNCEMENT_DATA_DIR):
        file_path = os.path.join(ANNOUNCEMENT_DATA_DIR, filename)
        try:
            if os.path.isfile(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    timestamp_str = data.get('timestamp', '')
                    if timestamp_str:
                        file_time = datetime.fromisoformat(timestamp_str)
                        if file_time < cutoff_time:
                            os.remove(file_path)
                            logging.info(f"🗑️ 删除旧公告数据: {filename}")
        except Exception as e:
            logging.error(f"❌ 清理公告数据时发生错误 {filename}: {e}")
    
    # 清理截图
    for filename in os.listdir(SCREENSHOTS_DIR):
        file_path = os.path.join(SCREENSHOTS_DIR, filename)
        try:
            if os.path.isfile(file_path):
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_mtime < cutoff_time:
                    os.remove(file_path)
                    logging.info(f"🗑️ 删除旧截图: {filename}")
        except Exception as e:
            logging.error(f"❌ 清理截图时发生错误 {filename}: {e}")
    
    # 清理旧日志文件
    for filename in os.listdir(LOG_DIR):
        if filename.startswith("monitor_") and filename.endswith(".log"):
            file_path = os.path.join(LOG_DIR, filename)
            try:
                if os.path.isfile(file_path):
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_mtime < cutoff_time:
                        os.remove(file_path)
                        logging.info(f"🗑️ 删除旧日志文件: {filename}")
            except Exception as e:
                logging.error(f"❌ 清理日志文件时发生错误 {filename}: {e}")
    
    logging.info("✅ 旧数据清理完成")

async def get_announcements(max_retries=3):
    logging.info("🔍 开始获取币安公告列表...")
    for attempt in range(max_retries):
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True, args=['--no-sandbox', '--disable-setuid-sandbox', '--disable-gpu'])
                context = await browser.new_context(
                    viewport={'width': 1280, 'height': 720},
                    no_viewport=True
                )
                page = await context.new_page()
                logging.info(f"📡 正在访问公告页面: {ANNOUNCEMENT_LIST}")
                await page.goto(ANNOUNCEMENT_LIST, timeout=30000)
                links = await page.locator("a.css-1ej4hfo").all()
                logging.info(f"🔗 找到 {len(links)} 个链接元素")
                items = []
                for link in links:
                    title = await link.inner_text()
                    href = await link.get_attribute("href")
                    if href and "/support/announcement/detail/" in href:
                        items.append({"title": title.strip(), "url": BASE_URL + href})
                await context.close()
                await browser.close()
                logging.info(f"✅ 成功获取 {len(items)} 个公告")
                return items
        except PlaywrightTimeoutError as e:
            logging.error(f"❌ 获取公告列表超时 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然超时")
                return []
        except PlaywrightError as e:
            logging.error(f"❌ Playwright 错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然失败")
                return []
        except Exception as e:
            logging.error(f"❌ 获取公告列表失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然失败")
                return []
    return []

async def get_content_and_screenshot(detail_url, ann_id, max_retries=3):
    """获取公告内容并截图，支持重试机制"""
    logging.info(f"📄 正在获取公告详情并截图: {detail_url}")
    for attempt in range(max_retries):
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True, args=['--no-sandbox', '--disable-setuid-sandbox', '--disable-gpu'])
                context = await browser.new_context(
                    viewport={'width': 1200, 'height': 800},
                    no_viewport=True
                )
                page = await context.new_page()
                
                await page.goto(detail_url, timeout=30000)
                await page.wait_for_timeout(3000)  # 等待页面完全加载
                
                # 获取内容
                try:
                    content = await page.locator("div.css-2hond7").inner_text()
                except Exception as content_error:
                    logging.warning(f"⚠️ 使用主选择器获取内容失败，尝试备用方案: {content_error}")
                    # 备用选择器
                    try:
                        content = await page.locator(".announcement-detail").inner_text()
                    except:
                        content = await page.locator("main").inner_text()
                
                # 截图
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_filename = f"announcement_{ann_id}_{timestamp}.png"
                screenshot_path = os.path.join(SCREENSHOTS_DIR, screenshot_filename)
                
                await page.screenshot(path=screenshot_path, full_page=True)
                logging.info(f"📸 页面截图已保存: {screenshot_path}")
                
                await context.close()
                await browser.close()
                
                content_preview = content.strip()[:100] + "..." if len(content.strip()) > 100 else content.strip()
                logging.info(f"✅ 成功获取公告内容 (长度: {len(content.strip())}): {content_preview}")
                
                # 保存公告内容到数据目录
                save_announcement_data(ann_id, content, detail_url)
                
                return content.strip(), screenshot_path
                
        except PlaywrightTimeoutError as e:
            logging.error(f"❌ 获取公告详情超时 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然超时")
                return "", None
        except PlaywrightError as e:
            logging.error(f"❌ Playwright 错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然失败")
                return "", None
        except Exception as e:
            logging.error(f"❌ 获取公告详情或截图失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = exponential_backoff(attempt)
                logging.info(f"⏰ 指数退避等待 {wait_time:.2f} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然失败")
                return "", None
    return "", None

def save_announcement_data(ann_id, content, url):
    """保存公告数据到JSON文件"""
    try:
        data = {
            "id": ann_id,
            "url": url,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        file_path = os.path.join(ANNOUNCEMENT_DATA_DIR, f"{ann_id}.json")
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logging.info(f"💾 公告数据已保存: {file_path}")
    except Exception as e:
        logging.error(f"❌ 保存公告数据时发生错误: {e}")

async def monitor_cycle():
    """单次监控循环"""
    try:
        # 加载已发送记录
        sent_ids = load_sent_ids()
        logging.info(f"📋 已发送记录数量: {len(sent_ids)}")
        
        # 获取公告列表
        announcements = await get_announcements()
        if not announcements:
            logging.warning("⚠️ 本次未获取到任何公告，将在下次循环中重试")
            return
        
        logging.info(f"🔍 开始处理 {len(announcements)} 个公告...")
        
        processed_count = 0
        skipped_count = 0
        related_count = 0
        
        for ann in announcements:
            try:
                ann_id = ann["url"].split("/")[-1]
                logging.info(f"\n📝 处理公告: {ann['title']} (ID: {ann_id})")
                
                if ann_id in sent_ids:
                    logging.info(f"⏭️ 公告 {ann_id} 已发送过，跳过")
                    skipped_count += 1
                    continue
                
                # 获取公告内容和截图
                content, screenshot_path = await get_content_and_screenshot(ann["url"], ann_id)
                if not content:
                    logging.error(f"❌ 无法获取公告内容，跳过公告 {ann_id}")
                    continue
                
                processed_count += 1
                
                # 检测内容是否发生变化
                logging.info(f"🔍 正在检测公告内容是否变化...")
                from utils import detect_content_change
                has_changed = detect_content_change(ann_id, content)
                
                if not has_changed:
                    logging.info(f"⏭️ 公告内容未变化，跳过进一步处理")
                    skipped_count += 1
                    continue
                
                # 使用关键词过滤系统检查内容
                logging.info(f"🔑 使用关键词系统过滤内容...")
                from keywords import filter_content
                is_relevant, matched_keywords = filter_content(content)
                
                if not is_relevant:
                    logging.info(f"🚫 公告内容未匹配到任何关键词，跳过进一步处理")
                    skipped_count += 1
                    save_sent_id(ann_id)
                    continue
                
                # 判断是否与 BN Alpha 相关
                logging.info(f"🤖 正在判断公告是否与 BN Alpha 相关...")
                try:
                    is_related = is_bn_alpha_related(content)
                    logging.info(f"🤖 AI判断结果: {'相关' if is_related else '不相关'}")
                    
                    if is_related:
                        related_count += 1
                        logging.info(f"📤 发送Alpha公告通知: {ann['title']}")
                        
                        # 構建Alpha公告信息
                        announcement_info = {
                            'id': ann_id,
                            'title': ann['title'],
                            'link': ann['url'],
                            'publish_time': datetime.now().strftime("%Y-%m-%d")
                        }
                        
                        # 使用專門的Alpha公告通知函數
                        success = send_alpha_notification_with_screenshot(announcement_info, screenshot_path)
                        
                        if success:
                            save_sent_id(ann_id)
                            logging.info(f"✅ Alpha公告通知發送成功，公告 {ann_id} 處理完成")
                        else:
                            logging.error(f"❌ Alpha公告通知發送失敗，公告 {ann_id}")
                    else:
                        logging.info(f"🚫 公告与 BN Alpha 无关，不发送通知")
                    # 即使不相关，也保存到已发送记录以避免重复处理
                    save_sent_id(ann_id)
                except Exception as e:
                    logging.error(f"❌ 判断公告相关性时发生错误 {ann_id}: {e}")
                    continue
            except Exception as e:
                logging.error(f"❌ 处理单个公告时发生错误: {e}")
                continue
        
        logging.info(f"\n📊 本次循环执行总结:")
        logging.info(f"   - 总公告数: {len(announcements)}")
        logging.info(f"   - 已跳过: {skipped_count}")
        logging.info(f"   - 已处理: {processed_count}")
        logging.info(f"   - BN Alpha相关: {related_count}")
        
    except Exception as e:
        logging.error(f"❌ 监控循环执行过程中发生严重错误: {e}")

def run_monitor_cycle():
    """运行监控循环，供调度器调用"""
    try:
        asyncio.run(monitor_cycle())
    except Exception as e:
        logging.error(f"❌ 运行监控循环时发生错误: {e}")

async def main():
    logging.info("🚀 开始执行持续监控程序...")
    
    # 发送启动测试消息
    logging.info("🧪 执行启动测试...")
    try:
        test_success = send_startup_test_message()
        if not test_success:
            logging.error("❌ 启动测试失败，但程序将继续运行以进行进一步调试...")
    except Exception as e:
        logging.error(f"❌ 启动测试时发生错误: {e}")
    
    # 初始化调度器
    scheduler = BackgroundScheduler()
    # 每5分钟运行一次监控循环
    scheduler.add_job(run_monitor_cycle, trigger=IntervalTrigger(minutes=5), id='monitor_cycle')
    # 每天凌晨运行一次清理旧数据任务
    scheduler.add_job(cleanup_old_data, trigger='cron', hour=0, minute=0, id='cleanup_old_data')
    logging.info("⏰ 调度器已初始化，每5分钟运行一次监控循环，每日凌晨清理旧数据")
    
    # 启动调度器
    scheduler.start()
    logging.info("🚀 调度器已启动，监控程序正在运行...")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logging.info("🛑 接收到中断信号，正在优雅退出...")
        scheduler.shutdown()
        logging.info("🏁 监控程序已退出")
    except Exception as e:
        logging.error(f"❌ 主程序中发生未预期的错误: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logging.error(f"❌ 启动程序时发生错误: {e}")
