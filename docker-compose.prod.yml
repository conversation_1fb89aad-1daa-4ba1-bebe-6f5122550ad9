version: '3.8'

services:
  bn_alpha_monitor:
    build: 
      context: .
      dockerfile: Dockerfile
    image: bn_alpha_monitor:latest
    container_name: bn_alpha_monitor_prod
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./screenshots:/app/screenshots
      # 只读挂载配置文件
      - ./config.py:/app/config.py:ro
    environment:
      - PYTHONUNBUFFERED=1
      - DISPLAY=:99
      # Ollama 配置
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-qwen2.5:3b}
      # 飞书配置
      - FEISHU_WEBHOOK=${FEISHU_WEBHOOK}
      # 监控配置
      - MONITOR_INTERVAL=${MONITOR_INTERVAL:-300}
      - BROWSER_HEADLESS=${BROWSER_HEADLESS:-true}
      - BROWSER_TIMEOUT=${BROWSER_TIMEOUT:-30000}
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=${LOG_FILE:-logs/monitor.log}
      # 存储配置
      - SCREENSHOTS_DIR=${SCREENSHOTS_DIR:-screenshots}
      - SENT_RECORDS_FILE=${SENT_RECORDS_FILE:-sent_records.json}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - bn_alpha_network
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false  # 需要写入日志和数据
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    # 用户配置（非root用户运行）
    user: "1000:1000"
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 可选：添加日志收集服务
  log_collector:
    image: fluent/fluent-bit:latest
    container_name: bn_alpha_log_collector
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/app:ro
      - ./fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
    depends_on:
      - bn_alpha_monitor
    networks:
      - bn_alpha_network
    profiles:
      - logging

  # 可选：添加监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: bn_alpha_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - bn_alpha_network
    profiles:
      - monitoring

networks:
  bn_alpha_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  prometheus_data:
