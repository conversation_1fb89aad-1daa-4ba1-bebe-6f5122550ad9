#!/bin/bash

# Binance Alpha 监控系统 Docker 运行脚本
# 使用方法: ./docker-run.sh [build|start|stop|restart|logs|status]

set -e

PROJECT_NAME="bn_alpha_monitor"
COMPOSE_FILE="docker-compose.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 检查环境变量
check_env() {
    log_info "检查环境变量..."
    
    if [ ! -f ".env" ]; then
        log_error ".env 文件不存在"
        log_info "请复制 .env.example 到 .env 并配置相应的值"
        exit 1
    fi
    
    # 检查关键环境变量
    source .env
    
    if [ -z "$FEISHU_WEBHOOK" ] || [ "$FEISHU_WEBHOOK" = "https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_here" ]; then
        log_warn "FEISHU_WEBHOOK 未正确配置"
    fi
    
    if [ -z "$OLLAMA_BASE_URL" ]; then
        log_warn "OLLAMA_BASE_URL 未配置，将使用默认值"
    fi
    
    log_info "环境变量检查完成"
}

# 构建镜像
build() {
    log_info "开始构建 Docker 镜像..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    log_info "镜像构建完成"
}

# 启动服务
start() {
    log_info "启动 $PROJECT_NAME 服务..."
    docker-compose -f $COMPOSE_FILE up -d
    log_info "服务启动完成"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 显示状态
    status
}

# 停止服务
stop() {
    log_info "停止 $PROJECT_NAME 服务..."
    docker-compose -f $COMPOSE_FILE down
    log_info "服务已停止"
}

# 重启服务
restart() {
    log_info "重启 $PROJECT_NAME 服务..."
    stop
    sleep 2
    start
}

# 查看日志
logs() {
    log_info "显示 $PROJECT_NAME 日志..."
    docker-compose -f $COMPOSE_FILE logs -f --tail=100
}

# 查看状态
status() {
    log_info "检查 $PROJECT_NAME 服务状态..."
    
    # 显示容器状态
    docker-compose -f $COMPOSE_FILE ps
    
    # 显示健康检查状态
    container_id=$(docker-compose -f $COMPOSE_FILE ps -q $PROJECT_NAME)
    if [ ! -z "$container_id" ]; then
        health_status=$(docker inspect --format='{{.State.Health.Status}}' $container_id 2>/dev/null || echo "unknown")
        log_info "健康检查状态: $health_status"
        
        if [ "$health_status" = "healthy" ]; then
            log_info "✅ 服务运行正常"
        elif [ "$health_status" = "unhealthy" ]; then
            log_error "❌ 服务健康检查失败"
        elif [ "$health_status" = "starting" ]; then
            log_warn "⏳ 服务正在启动中..."
        else
            log_warn "❓ 无法获取健康检查状态"
        fi
    fi
}

# 清理
clean() {
    log_info "清理 Docker 资源..."
    docker-compose -f $COMPOSE_FILE down -v --rmi all
    docker system prune -f
    log_info "清理完成"
}

# 显示帮助
help() {
    echo "Binance Alpha 监控系统 Docker 管理脚本"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  build    - 构建 Docker 镜像"
    echo "  start    - 启动服务"
    echo "  stop     - 停止服务"
    echo "  restart  - 重启服务"
    echo "  logs     - 查看日志"
    echo "  status   - 查看服务状态"
    echo "  clean    - 清理 Docker 资源"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build && $0 start    # 构建并启动"
    echo "  $0 logs                 # 查看实时日志"
    echo "  $0 status               # 检查服务状态"
}

# 主函数
main() {
    case "${1:-help}" in
        build)
            check_dependencies
            check_env
            build
            ;;
        start)
            check_dependencies
            check_env
            start
            ;;
        stop)
            check_dependencies
            stop
            ;;
        restart)
            check_dependencies
            check_env
            restart
            ;;
        logs)
            check_dependencies
            logs
            ;;
        status)
            check_dependencies
            status
            ;;
        clean)
            check_dependencies
            clean
            ;;
        help|--help|-h)
            help
            ;;
        *)
            log_error "未知命令: $1"
            help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
