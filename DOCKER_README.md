# Binance Alpha 监控系统 Docker 部署指南

## 📋 概述

本项目是一个基于 Docker 的 Binance Alpha 监控系统，用于自动监控币安公告并通过飞书推送相关信息。

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Docker (>= 20.10)
- Docker Compose (>= 1.29)

### 2. 配置环境变量

复制并编辑环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：
```bash
# 飞书通知配置（必需）
FEISHU_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_here

# Ollama API 配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:3b

# 监控配置
MONITOR_INTERVAL=300  # 5分钟
BROWSER_HEADLESS=true
```

### 3. 使用管理脚本（推荐）

我们提供了便捷的管理脚本：

```bash
# 构建并启动服务
./docker-run.sh build
./docker-run.sh start

# 或者一键构建启动
./docker-run.sh build && ./docker-run.sh start

# 查看服务状态
./docker-run.sh status

# 查看实时日志
./docker-run.sh logs

# 重启服务
./docker-run.sh restart

# 停止服务
./docker-run.sh stop
```

### 4. 手动 Docker Compose 操作

如果不使用管理脚本，也可以直接使用 Docker Compose：

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📁 项目结构

```
bn_alpha_playwright_agent/
├── Dockerfile                 # Docker 镜像定义
├── docker-compose.yml         # 开发环境配置
├── docker-compose.prod.yml    # 生产环境配置
├── docker-run.sh             # 管理脚本
├── healthcheck.py            # 健康检查脚本
├── .dockerignore             # Docker 构建忽略文件
├── requirements.txt          # Python 依赖
├── .env                      # 环境变量配置
├── monitor.py               # 主监控程序
├── config.py                # 配置模块
├── agent_agno.py            # AI 分析模块
├── feishu_notify.py         # 飞书通知模块
├── utils.py                 # 工具函数
├── logs/                    # 日志目录（挂载）
├── data/                    # 数据目录（挂载）
└── screenshots/             # 截图目录（挂载）
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `FEISHU_WEBHOOK` | 飞书机器人 Webhook URL | - | ✅ |
| `OLLAMA_BASE_URL` | Ollama API 地址 | `http://localhost:11434` | ❌ |
| `OLLAMA_MODEL` | 使用的 Ollama 模型 | `qwen2.5:3b` | ❌ |
| `MONITOR_INTERVAL` | 监控间隔（秒） | `300` | ❌ |
| `BROWSER_HEADLESS` | 无头浏览器模式 | `true` | ❌ |
| `LOG_LEVEL` | 日志级别 | `INFO` | ❌ |

### 数据持久化

以下目录会被挂载到宿主机，确保数据持久化：
- `./logs` - 日志文件
- `./data` - 监控数据
- `./screenshots` - 页面截图

## 🏥 健康检查

系统内置健康检查功能，会定期检查：
- 日志文件更新状态
- 必要目录存在性
- 发送记录文件完整性

查看健康状态：
```bash
docker-compose ps
# 或
./docker-run.sh status
```

## 📊 监控和日志

### 查看日志
```bash
# 实时日志
./docker-run.sh logs

# 或使用 Docker Compose
docker-compose logs -f bn_alpha_monitor
```

### 日志文件位置
- 容器内：`/app/logs/`
- 宿主机：`./logs/`

## 🔒 安全配置

### 生产环境部署

使用生产环境配置：
```bash
docker-compose -f docker-compose.prod.yml up -d
```

生产环境特性：
- 非 root 用户运行
- 资源限制
- 安全选项配置
- 日志轮转
- 只读文件系统（部分）

### 网络安全
- 使用自定义网络
- 限制容器间通信
- 配置防火墙规则

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs bn_alpha_monitor
   
   # 检查环境变量
   ./docker-run.sh status
   ```

2. **Playwright 浏览器问题**
   ```bash
   # 重新构建镜像
   ./docker-run.sh build
   ```

3. **权限问题**
   ```bash
   # 检查目录权限
   ls -la logs/ data/ screenshots/
   
   # 修复权限
   sudo chown -R $USER:$USER logs/ data/ screenshots/
   ```

4. **网络连接问题**
   ```bash
   # 测试网络连接
   docker exec bn_alpha_monitor ping www.binance.com
   ```

### 调试模式

启用调试模式：
```bash
# 修改 .env 文件
LOG_LEVEL=DEBUG

# 重启服务
./docker-run.sh restart
```

## 🔄 更新和维护

### 更新代码
```bash
# 停止服务
./docker-run.sh stop

# 拉取最新代码
git pull

# 重新构建并启动
./docker-run.sh build
./docker-run.sh start
```

### 清理资源
```bash
# 清理所有 Docker 资源
./docker-run.sh clean
```

## 📞 支持

如果遇到问题，请：
1. 查看日志文件
2. 检查环境变量配置
3. 确认网络连接
4. 查看 GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证。
