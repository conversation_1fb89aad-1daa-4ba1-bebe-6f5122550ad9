import os
import json
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

KEYWORDS_FILE = "keywords.json"

# 默认关键词列表，适用于BN Alpha机会
DEFAULT_KEYWORDS = [
    "alpha",
    "opportunity",
    "investment",
    "fund",
    "venture",
    "capital",
    "startup",
    "innovation",
    "technology",
    "blockchain",
    "crypto",
    "token",
    "ICO",
    "IDO",
    "launchpad",
    "binance",
    "BNB",
    "smart chain",
    "defi",
    "nft",
    "metaverse",
    "web3"
]

def load_keywords():
    """从JSON文件加载关键词列表，如果文件不存在则创建默认列表"""
    if not os.path.exists(KEYWORDS_FILE):
        logging.info(f"🔑 关键词文件 {KEYWORDS_FILE} 不存在，创建默认关键词列表")
        save_keywords(DEFAULT_KEYWORDS)
        return DEFAULT_KEYWORDS
    
    try:
        with open(KEYWORDS_FILE, "r") as f:
            keywords = json.load(f)
        logging.info(f"🔑 成功加载 {len(keywords)} 个关键词")
        return keywords
    except Exception as e:
        logging.error(f"❌ 加载关键词列表时发生错误: {e}")
        logging.info(f"🔑 使用默认关键词列表")
        return DEFAULT_KEYWORDS

def save_keywords(keywords):
    """将关键词列表保存到JSON文件"""
    try:
        with open(KEYWORDS_FILE, "w") as f:
            json.dump(keywords, f, ensure_ascii=False, indent=2)
        logging.info(f"✅ 成功保存 {len(keywords)} 个关键词到 {KEYWORDS_FILE}")
    except Exception as e:
        logging.error(f"❌ 保存关键词列表时发生错误: {e}")

def add_keyword(keyword):
    """添加一个新关键词到列表中"""
    keywords = load_keywords()
    if keyword.lower() not in [k.lower() for k in keywords]:
        keywords.append(keyword)
        save_keywords(keywords)
        logging.info(f"➕ 添加新关键词: {keyword}")
    else:
        logging.info(f"🔑 关键词 {keyword} 已存在")

def remove_keyword(keyword):
    """从列表中移除一个关键词"""
    keywords = load_keywords()
    if keyword in keywords:
        keywords.remove(keyword)
        save_keywords(keywords)
        logging.info(f"➖ 移除关键词: {keyword}")
    else:
        logging.info(f"🔑 关键词 {keyword} 不存在")

def filter_content(content, keywords=None):
    """根据关键词过滤内容，返回是否匹配以及匹配的关键词列表"""
    if keywords is None:
        keywords = load_keywords()
    
    content_lower = content.lower()
    matched_keywords = [keyword for keyword in keywords if keyword.lower() in content_lower]
    
    if matched_keywords:
        logging.info(f"🔍 内容匹配到关键词: {', '.join(matched_keywords)}")
        return True, matched_keywords
    else:
        logging.info(f"🔍 内容未匹配到任何关键词")
        return False, []
