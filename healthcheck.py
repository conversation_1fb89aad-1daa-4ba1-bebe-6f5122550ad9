#!/usr/bin/env python3
"""
健康检查脚本
用于 Docker 容器健康检查
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

def check_log_file():
    """检查日志文件是否正常更新"""
    try:
        log_dir = Path("logs")
        if not log_dir.exists():
            return False, "日志目录不存在"
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob("monitor_*.log"))
        if not log_files:
            return False, "没有找到监控日志文件"
        
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        # 检查日志文件是否在最近30分钟内有更新
        last_modified = datetime.fromtimestamp(latest_log.stat().st_mtime)
        if datetime.now() - last_modified > timedelta(minutes=30):
            return False, f"日志文件超过30分钟未更新: {latest_log}"
        
        return True, f"日志文件正常: {latest_log}"
    except Exception as e:
        return False, f"检查日志文件时出错: {e}"

def check_sent_records():
    """检查发送记录文件"""
    try:
        sent_records_file = Path("sent_records.json")
        if not sent_records_file.exists():
            # 文件不存在是正常的（首次运行）
            return True, "发送记录文件不存在（首次运行）"
        
        # 检查文件是否可读
        with open(sent_records_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return True, f"发送记录文件正常，包含 {len(data)} 条记录"
    except Exception as e:
        return False, f"检查发送记录文件时出错: {e}"

def check_directories():
    """检查必要的目录是否存在"""
    required_dirs = ["logs", "data", "screenshots"]
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        return False, f"缺少必要目录: {', '.join(missing_dirs)}"
    
    return True, "所有必要目录都存在"

def main():
    """主健康检查函数"""
    checks = [
        ("目录检查", check_directories),
        ("日志文件检查", check_log_file),
        ("发送记录检查", check_sent_records),
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        try:
            passed, message = check_func()
            results.append(f"{check_name}: {'✅' if passed else '❌'} {message}")
            if not passed:
                all_passed = False
        except Exception as e:
            results.append(f"{check_name}: ❌ 检查时发生异常: {e}")
            all_passed = False
    
    # 输出结果
    print(f"健康检查结果 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}):")
    for result in results:
        print(f"  {result}")
    
    if all_passed:
        print("✅ 所有健康检查通过")
        sys.exit(0)
    else:
        print("❌ 部分健康检查失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
