import os
import requests
import logging
import base64
from dotenv import load_dotenv
import time
import mimetypes

# 加载 .env 文件中的环境变量
load_dotenv()

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 验证 .env 文件是否被正确加载
logging.info("load_dotenv() 已调用，开始检查环境变量...")

FEISHU_WEBHOOK = os.getenv("FEISHU_WEBHOOK")
logging.info(f"Loaded FEISHU_WEBHOOK: {FEISHU_WEBHOOK}") # 记录加载的 webhook 地址

# 验证环境变量是否成功加载
if FEISHU_WEBHOOK:
    logging.info("✅ FEISHU_WEBHOOK 环境变量加载成功！")
else:
    logging.error("❌ FEISHU_WEBHOOK 环境变量仍为 None，请检查 .env 文件路径和格式")

def upload_image_to_feishu(image_path):
    """处理图片并返回图片信息"""
    if not os.path.exists(image_path):
        logging.error(f"❌ 图片文件不存在: {image_path}")
        return None
    
    # 检查文件大小 (飞书限制为30MB)
    file_size = os.path.getsize(image_path)
    if file_size > 10 * 1024 * 1024:  # 限制为10MB以提高发送成功率
        logging.error(f"❌ 图片文件太大: {file_size} bytes > 10MB")
        return None
    
    # 检测文件类型
    mime_type, _ = mimetypes.guess_type(image_path)
    if not mime_type or not mime_type.startswith('image/'):
        logging.error(f"❌ 不支持的文件类型: {mime_type}")
        return None
    
    try:
        # 读取图片文件
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 将图片转换为base64编码
        base64_image = base64.b64encode(image_data).decode('utf-8')
        
        # 限制base64字符串大小（飞书消息有大小限制）
        if len(base64_image) > 1000000:  # 1MB base64 字符串
            logging.warning(f"⚠️ 图片base64太大 ({len(base64_image)} 字符)，将跳过图片发送")
            return None
        
        logging.info(f"✅ 图片已转换为base64格式，大小: {len(base64_image)} 字符")
        return {
            'base64': base64_image,
            'mime_type': mime_type,
            'file_size': file_size,
            'file_path': image_path
        }
        
    except Exception as e:
        logging.error(f"❌ 处理图片时发生错误: {e}")
        return None

def send_feishu_message(title, url, screenshot_path=None, priority="high", max_retries=3):
    """发送飞书通知消息，支持富文本格式、优先级和图片，并为网络错误添加重试机制"""
    logging.info(f"📤 开始发送飞书通知...")
    logging.info(f"📤 标题: {title}")
    logging.info(f"📤 链接: {url}")
    logging.info(f"📤 优先级: {priority}")
    if screenshot_path:
        logging.info(f"📤 截图路径: {screenshot_path}")
    
    if not FEISHU_WEBHOOK:
        logging.error("❌ FEISHU_WEBHOOK 未设置，无法发送消息")
        return False

    # 根据优先级设置不同的图标和颜色
    if priority == "high":
        priority_icon = "🔴"
        priority_color = "#FF0000"  # 红色
    elif priority == "medium":
        priority_icon = "🟠"
        priority_color = "#FFA500"  # 橙色
    else:
        priority_icon = "🟢"
        priority_color = "#00FF00"  # 绿色

    # 尝试处理图片
    image_info = None
    if screenshot_path and os.path.exists(screenshot_path):
        logging.info(f"📸 尝试处理截图: {screenshot_path}")
        image_info = upload_image_to_feishu(screenshot_path)
        if image_info:
            logging.info(f"✅ 截图已准备好发送")
        else:
            logging.warning(f"⚠️ 截图处理失败，将发送纯文本消息")

    # 嘗試發送富文本格式（支持圖片）
    try:
        if image_info and len(image_info['base64']) < 500000:  # 限制图片大小以确保发送成功
            # 使用富文本格式，支持图片显示
            content = {
                "zh_cn": {
                    "title": f"{priority_icon} BN Alpha 公告通知",
                    "content": [
                        [
                            {
                                "tag": "text",
                                "text": f"📢 {title}\n\n"
                            }
                        ],
                        [
                            {
                                "tag": "a",
                                "text": "🔗 查看詳情",
                                "href": url
                            }
                        ],
                        [
                            {
                                "tag": "text",
                                "text": "\n📸 內頁截圖："
                            }
                        ],
                        [
                            {
                                "tag": "img",
                                "image_key": f"data:{image_info['mime_type']};base64,{image_info['base64']}"
                            }
                        ]
                    ]
                }
            }
            
            payload = {
                "msg_type": "post",
                "content": content
            }
            
            logging.info(f"📤 嘗試發送富文本格式消息（包含圖片）...")
            response = requests.post(FEISHU_WEBHOOK, json=payload, timeout=30)
            
            if response.status_code == 200:
                logging.info(f"✅ 富文本消息（含圖片）發送成功！")
                return True
            else:
                logging.warning(f"⚠️ 富文本消息發送失敗，狀態碼: {response.status_code}，回退到文本格式")
        
    except Exception as e:
        logging.warning(f"⚠️ 富文本格式發送失敗: {e}，回退到文本格式")

    # 构建纯文本消息内容
    message_text = f"{priority_icon} BN Alpha 公告通知 - {priority.upper()}\n\n"
    message_text += f"📢 {title}\n\n"
    message_text += f"🔗 查看详情: {url}"
    
    if image_info:
        file_size_mb = image_info['file_size'] / (1024 * 1024)
        message_text += f"\n\n📸 內頁截圖已保存:"
        message_text += f"\n   📁 路径: {image_info['file_path']}"
        message_text += f"\n   📊 大小: {file_size_mb:.2f} MB"
        message_text += f"\n   🖼️ 类型: {image_info['mime_type']}"
        
        # 如果圖片不太大，添加提示說明可以查看截圖
        if file_size_mb < 5:
            message_text += f"\n\n💡 截圖文件已準備好，包含完整的內頁內容"
    elif screenshot_path and os.path.exists(screenshot_path):
        message_text += f"\n\n📸 页面截图已保存: {screenshot_path}"

    payload = {
        "msg_type": "text",
        "content": {
            "text": message_text
        }
    }
    
    logging.info(f"📤 消息类型: {payload['msg_type']}")
    logging.info(f"📤 发送到: {FEISHU_WEBHOOK}")
    
    for attempt in range(max_retries):
        try:
            response = requests.post(FEISHU_WEBHOOK, json=payload, timeout=30)
            logging.info(f"📤 HTTP 状态码: {response.status_code}")
            logging.info(f"📤 响应内容: {response.text}")
            
            response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则抛出异常
            logging.info(f"✅ 飞书消息发送成功！")
            return True
            
        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            logging.error(f"❌ 网络相关错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 5  # 递增等待时间
                logging.info(f"⏰ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                logging.error(f"❌ 经过 {max_retries} 次尝试后仍然失败")
                return False
                
        except requests.exceptions.HTTPError as e:
            logging.error(f"❌ HTTP 错误: {e}")
            return False
            
        except requests.exceptions.RequestException as e:
            logging.error(f"❌ 请求异常: {e}")
            return False
            
        except Exception as e:
            logging.error(f"❌ 发送飞书消息时发生未知错误: {e}")
            return False

def send_alpha_notification_with_screenshot(announcement, screenshot_path=None):
    """
    發送Alpha公告通知，包含截圖
    
    Args:
        announcement: 公告信息字典，包含title, link等
        screenshot_path: 截圖文件路徑
        
    Returns:
        bool: 發送是否成功
    """
    logging.info(f"🚨 發送Alpha公告通知...")
    
    title = announcement.get('title', '未知公告')
    url = announcement.get('link', '')
    publish_time = announcement.get('publish_time', '')
    
    # 構建Alpha公告格式的標題
    if publish_time:
        formatted_title = f"{title} ({publish_time})"
    else:
        formatted_title = title
    
    # 發送通知，高優先級
    success = send_feishu_message(
        title=formatted_title,
        url=url,
        screenshot_path=screenshot_path,
        priority="high"
    )
    
    if success:
        logging.info(f"✅ Alpha公告通知發送成功: {title[:50]}...")
    else:
        logging.error(f"❌ Alpha公告通知發送失敗: {title[:50]}...")
    
    return success

def send_batch_alpha_notifications(alpha_announcements, screenshots_dir="screenshots"):
    """
    批量發送Alpha公告通知
    
    Args:
        alpha_announcements: Alpha公告列表
        screenshots_dir: 截圖目錄
        
    Returns:
        dict: 發送結果統計
    """
    logging.info(f"📢 開始批量發送 {len(alpha_announcements)} 個Alpha公告通知...")
    
    success_count = 0
    failed_count = 0
    results = []
    
    # 獲取最新的截圖文件
    screenshot_files = []
    if os.path.exists(screenshots_dir):
        screenshot_files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
        screenshot_files.sort(key=lambda x: os.path.getctime(os.path.join(screenshots_dir, x)), reverse=True)
    
    for i, announcement in enumerate(alpha_announcements):
        try:
            # 為每個公告分配截圖（如果有的話）
            screenshot_path = None
            if screenshot_files and i < len(screenshot_files):
                screenshot_path = os.path.join(screenshots_dir, screenshot_files[i])
            elif screenshot_files:
                # 如果截圖數量不夠，使用最新的截圖
                screenshot_path = os.path.join(screenshots_dir, screenshot_files[0])
            
            success = send_alpha_notification_with_screenshot(announcement, screenshot_path)
            
            result = {
                'announcement_id': announcement.get('id'),
                'title': announcement.get('title'),
                'success': success,
                'screenshot_used': screenshot_path
            }
            results.append(result)
            
            if success:
                success_count += 1
            else:
                failed_count += 1
            
            # 在發送之間添加短暫延遲，避免過於頻繁
            if i < len(alpha_announcements) - 1:
                time.sleep(2)
                
        except Exception as e:
            logging.error(f"❌ 發送第 {i+1} 個Alpha公告時出錯: {e}")
            failed_count += 1
            results.append({
                'announcement_id': announcement.get('id'),
                'title': announcement.get('title'),
                'success': False,
                'error': str(e)
            })
    
    summary = {
        'total': len(alpha_announcements),
        'success_count': success_count,
        'failed_count': failed_count,
        'success_rate': (success_count / len(alpha_announcements) * 100) if alpha_announcements else 0,
        'results': results
    }
    
    logging.info(f"📊 批量發送完成: {success_count}/{len(alpha_announcements)} 成功")
    return summary

def send_startup_test_message():
    """发送启动测试消息，验证飞书 Webhook 是否正常工作"""
    import datetime
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    test_title = f"🤖 BN Alpha 监控服务启动测试"
    test_url = "https://www.binance.com/zh-CN/support/announcement/list/48"
    
    logging.info("🧪 发送启动测试消息...")
    success = send_feishu_message(test_title, test_url, priority="medium")
    
    if success:
        logging.info("✅ 启动测试消息发送成功，飞书 Webhook 工作正常！")
    else:
        logging.error("❌ 启动测试消息发送失败，请检查飞书 Webhook 配置！")
    
    return success
