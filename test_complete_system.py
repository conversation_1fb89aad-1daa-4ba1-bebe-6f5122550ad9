#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系統測試 - 測試Alpha公告通知系統的端到端功能
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
import tempfile
import shutil

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from feishu_notify import send_alpha_notification_with_screenshot, send_startup_test_message
from agent_agno import is_bn_alpha_related
from utils import load_sent_ids, save_sent_id

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'test_complete_system_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
    ]
)

def create_test_screenshot():
    """創建測試截圖文件"""
    screenshots_dir = "screenshots"
    os.makedirs(screenshots_dir, exist_ok=True)
    
    # 創建一個簡單的測試圖片文件
    import base64
    
    # 1x1像素的透明PNG圖片的base64編碼
    test_image_data = base64.b64decode(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA2w6pKgAAAABJRU5ErkJggg=='
    )
    
    test_screenshot_path = os.path.join(screenshots_dir, f"test_alpha_notification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    
    with open(test_screenshot_path, 'wb') as f:
        f.write(test_image_data)
    
    logging.info(f"✅ 測試截圖已創建: {test_screenshot_path}")
    return test_screenshot_path

async def test_alpha_content_detection():
    """測試Alpha相關內容檢測"""
    logging.info("🧪 測試Alpha相關內容檢測...")
    
    # 測試Alpha相關內容
    alpha_content = """
    幣安Alpha和幣安合約將上線Defi App (HOME) 
    
    親愛的用戶：
    
    幣安將於2025年1月15日18:00（東八區時間）在Alpha專區上線HOME (Defi App)，並開放HOME/USDT交易對。
    
    Alpha專區是幣安推出的全新功能，旨在為用戶提供早期優質項目的投資機會。
    
    請注意以下重要信息：
    1. Alpha專區代幣具有較高的市場風險和波動性
    2. 建議用戶在交易前充分了解項目風險
    3. 請謹慎投資，量力而行
    
    感謝您的支持！
    幣安團隊
    """
    
    try:
        is_alpha_related = is_bn_alpha_related(alpha_content)
        logging.info(f"✅ Alpha內容檢測結果: {'相關' if is_alpha_related else '不相關'}")
        
        if is_alpha_related:
            logging.info("✅ Alpha內容檢測測試通過")
            return True
        else:
            logging.error("❌ Alpha內容檢測測試失敗 - 應該識別為相關但實際為不相關")
            return False
            
    except Exception as e:
        logging.error(f"❌ Alpha內容檢測測試失敗: {e}")
        return False

async def test_alpha_notification():
    """測試Alpha公告通知功能"""
    logging.info("🧪 測試Alpha公告通知功能...")
    
    # 創建測試截圖
    test_screenshot_path = create_test_screenshot()
    
    # 測試公告信息
    test_announcement = {
        'id': f'test_alpha_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'title': '幣安Alpha和幣安合約將上線Defi App (HOME) (測試)',
        'link': 'https://www.binance.com/zh-CN/support/announcement/detail/test_alpha_announcement',
        'publish_time': datetime.now().strftime("%Y-%m-%d")
    }
    
    try:
        # 發送Alpha公告通知
        success = send_alpha_notification_with_screenshot(test_announcement, test_screenshot_path)
        
        if success:
            logging.info("✅ Alpha公告通知測試通過")
            return True
        else:
            logging.error("❌ Alpha公告通知測試失敗")
            return False
            
    except Exception as e:
        logging.error(f"❌ Alpha公告通知測試失敗: {e}")
        return False
    finally:
        # 清理測試截圖
        try:
            if os.path.exists(test_screenshot_path):
                os.remove(test_screenshot_path)
                logging.info(f"🧹 測試截圖已清理: {test_screenshot_path}")
        except Exception as e:
            logging.warning(f"⚠️ 清理測試截圖時發生錯誤: {e}")

async def test_startup_message():
    """測試啟動消息"""
    logging.info("🧪 測試啟動消息...")
    
    try:
        success = send_startup_test_message()
        
        if success:
            logging.info("✅ 啟動消息測試通過")
            return True
        else:
            logging.error("❌ 啟動消息測試失敗")
            return False
            
    except Exception as e:
        logging.error(f"❌ 啟動消息測試失敗: {e}")
        return False

async def run_complete_system_test():
    """運行完整系統測試"""
    logging.info("🚀 開始完整系統測試...")
    
    test_results = []
    
    # 測試1: 啟動消息
    logging.info("\n" + "="*50)
    logging.info("測試 1: 啟動消息測試")
    logging.info("="*50)
    result1 = await test_startup_message()
    test_results.append(("啟動消息測試", result1))
    
    # 測試2: Alpha內容檢測
    logging.info("\n" + "="*50)
    logging.info("測試 2: Alpha內容檢測測試")
    logging.info("="*50)
    result2 = await test_alpha_content_detection()
    test_results.append(("Alpha內容檢測測試", result2))
    
    # 測試3: Alpha公告通知
    logging.info("\n" + "="*50)
    logging.info("測試 3: Alpha公告通知測試")
    logging.info("="*50)
    result3 = await test_alpha_notification()
    test_results.append(("Alpha公告通知測試", result3))
    
    # 總結測試結果
    logging.info("\n" + "="*50)
    logging.info("📊 測試結果總結")
    logging.info("="*50)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        logging.info(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    logging.info(f"\n總體結果: {passed_count}/{total_count} 測試通過")
    
    if passed_count == total_count:
        logging.info("🎉 所有測試都通過！系統功能正常")
        return True
    else:
        logging.error(f"⚠️ 有 {total_count - passed_count} 個測試失敗，請檢查相關功能")
        return False

def main():
    """主函數"""
    try:
        # 運行完整系統測試
        success = asyncio.run(run_complete_system_test())
        
        if success:
            print("\n🎉 完整系統測試通過！")
            sys.exit(0)
        else:
            print("\n❌ 完整系統測試失敗！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logging.info("🛑 測試被用戶中斷")
        sys.exit(1)
    except Exception as e:
        logging.error(f"❌ 測試過程中發生嚴重錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
