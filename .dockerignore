# 忽略版本控制系统文件
.git
.gitignore

# 忽略 IDE 和编辑器配置文件
.idea
.vscode
*.swp
*.swo

# 忽略日志文件和数据目录
logs/
data/
screenshots/

# 忽略敏感环境变量文件（保留 .env 用于配置）
.env.local
.env.development
.env.test
.env.production

# 忽略 Python 缓存和虚拟环境
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
env.bak/
venv.bak/

# 忽略 Docker 相关文件
docker-compose.override.yml

# 忽略 Task Master 相关目录
.taskmaster/
.cursor/

# 忽略其他临时文件
*.log
*.tmp
temp/
