import json
import os
import logging
from difflib import unified_diff

SENT_FILE = "sent_records.json"
CONTENT_SNAPSHOTS_FILE = "page_state.json"

def load_sent_ids():
    """Load the set of sent announcement IDs from a JSON file."""
    if not os.path.exists(SENT_FILE):
        return set()
    with open(SENT_FILE, "r") as f:
        return set(json.load(f))

def save_sent_id(ann_id):
    """Save a new announcement ID to the sent records."""
    data = load_sent_ids()
    data.add(ann_id)
    with open(SENT_FILE, "w") as f:
        json.dump(list(data), f)

def load_content_snapshots():
    """Load content snapshots from a JSON file for comparison."""
    if not os.path.exists(CONTENT_SNAPSHOTS_FILE):
        return {}
    try:
        with open(CONTENT_SNAPSHOTS_FILE, "r") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Error loading content snapshots: {e}")
        return {}

def save_content_snapshot(ann_id, content):
    """Save the content snapshot for a given announcement ID."""
    snapshots = load_content_snapshots()
    snapshots[ann_id] = content
    try:
        with open(CONTENT_SNAPSHOTS_FILE, "w") as f:
            json.dump(snapshots, f, ensure_ascii=False, indent=2)
        logging.info(f"Saved content snapshot for announcement {ann_id}")
    except Exception as e:
        logging.error(f"Error saving content snapshot for {ann_id}: {e}")

def detect_content_change(ann_id, current_content):
    """Detect if the content of an announcement has changed significantly."""
    snapshots = load_content_snapshots()
    if ann_id not in snapshots:
        logging.info(f"No previous snapshot for {ann_id}, treating as new content")
        save_content_snapshot(ann_id, current_content)
        return True
    
    previous_content = snapshots[ann_id]
    if previous_content == current_content:
        logging.info(f"No change detected for announcement {ann_id}")
        return False
    
    # Calculate differences using unified_diff for detailed comparison
    diff = list(unified_diff(
        previous_content.splitlines(), 
        current_content.splitlines(), 
        fromfile="previous_" + ann_id, 
        tofile="current_" + ann_id, 
        lineterm=""
    ))
    diff_text = "\n".join(diff)
    logging.info(f"Content change detected for {ann_id}:\n{diff_text}")
    
    # Update snapshot with new content
    save_content_snapshot(ann_id, current_content)
    return True
