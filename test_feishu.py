#!/usr/bin/env python3
"""
飞书 Webhook 测试脚本
用于验证飞书通知功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from feishu_notify import send_startup_test_message, send_feishu_message
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    print("=" * 60)
    print("🧪 飞书 Webhook 测试脚本")
    print("=" * 60)
    
    # 测试1: 启动测试消息
    print("\n📋 测试1: 发送启动测试消息")
    print("-" * 40)
    success1 = send_startup_test_message()
    
    # 测试2: 模拟公告通知
    print("\n📋 测试2: 发送模拟 BN Alpha 公告通知")
    print("-" * 40)
    test_title = "测试公告: Binance Alpha 新项目上线"
    test_url = "https://www.binance.com/zh-CN/support/announcement/test"
    success2 = send_feishu_message(test_title, test_url)
    
    # 结果总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   ✅ 启动测试消息: {'成功' if success1 else '失败'}")
    print(f"   ✅ 公告通知消息: {'成功' if success2 else '失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！飞书 Webhook 工作正常。")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查:")
        print("   1. FEISHU_WEBHOOK 环境变量是否正确设置")
        print("   2. 网络连接是否正常")
        print("   3. 飞书机器人 Webhook 地址是否有效")
        return 1

if __name__ == "__main__":
    exit(main())