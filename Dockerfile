# 使用更新的 Python 镜像作为基础镜像（修复安全漏洞）
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 安装 Playwright 浏览器（使用官方安装脚本）
RUN playwright install chromium && \
    playwright install-deps chromium

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs data screenshots

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 启动 Binance Alpha 监控系统..."\n\
echo "📊 检查环境变量..."\n\
if [ -z "$FEISHU_WEBHOOK" ]; then\n\
    echo "⚠️  警告: FEISHU_WEBHOOK 未设置"\n\
fi\n\
echo "🔧 启动监控程序..."\n\
python monitor.py' > /app/start.sh && chmod +x /app/start.sh

# 运行启动脚本
CMD ["/app/start.sh"]
