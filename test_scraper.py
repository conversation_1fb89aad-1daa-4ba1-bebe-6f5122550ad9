#!/usr/bin/env python3
"""
测试公告抓取功能
"""
import asyncio
import json
from pathlib import Path
from agent_agno import monitor_binance_announcements, fetch_page_content, parse_binance_announcements
from config import config

def test_announcement_scraping():
    """测试公告抓取功能"""
    print("🔧 开始测试公告抓取功能...")
    
    try:
        # 加载配置
        print("✅ 配置加载成功")
        
        # 测试抓取功能
        print("🌐 开始抓取币安公告...")
        announcements = monitor_binance_announcements()
        
        if announcements:
            print(f"✅ 成功抓取到 {len(announcements)} 个公告")
            
            # 显示前3个公告的详细信息
            print("📋 抓取到的公告样例:")
            for i, announcement in enumerate(announcements[:3], 1):
                print(f"\n公告 {i}:")
                print(f"  - ID: {announcement.get('id', 'N/A')}")
                print(f"  - 标题: {announcement.get('title', 'N/A')[:60]}...")
                print(f"  - 链接: {announcement.get('link', 'N/A')[:80]}...")
                print(f"  - 发布时间: {announcement.get('publish_time', 'N/A')}")
                print(f"  - 提取时间: {announcement.get('extracted_at', 'N/A')}")
            
            if len(announcements) > 3:
                print(f"\n... 还有 {len(announcements) - 3} 个公告")
                
            return True
        else:
            print("⚠️ 未抓取到任何公告，但抓取功能正常")
            return True
            
    except Exception as e:
        print(f"❌ 抓取测试失败: {str(e)}")
        return False

async def test_page_fetching():
    """测试页面获取功能"""
    print("🌐 测试页面获取功能...")
    
    try:
        # 测试获取币安公告页面
        test_url = "https://www.binance.com/zh-CN/support/announcement/list/48"
        print(f"📡 正在获取页面: {test_url}")
        
        html_content = await fetch_page_content(test_url)
        
        if html_content:
            print(f"✅ 成功获取页面内容，长度: {len(html_content)} 字符")
            
            # 基本内容检查
            if 'announcement' in html_content.lower() or 'alpha' in html_content.lower():
                print("✅ 页面包含预期的公告相关内容")
            else:
                print("⚠️ 页面可能不包含预期内容，但获取功能正常")
                
            return True
        else:
            print("❌ 页面获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 页面获取测试失败: {str(e)}")
        return False

def test_html_parsing():
    """测试HTML解析功能"""
    print("🔍 测试HTML解析功能...")
    
    try:
        # 使用模拟数据测试解析功能
        mock_html = """
        <html>
        <body>
        <div class="announcement-list">
            <a class="bn-balink" href="/zh-CN/support/announcement/detail/test1">
                <h3>币安Alpha和币安合约将上线Defi App (HOME)</h3>
            </a>
            <div>2025-06-09</div>
            
            <a class="bn-balink" href="/zh-CN/support/announcement/detail/test2">
                <h3>币安Alpha和币安合约将上线Skate (SKATE)</h3>
            </a>
            <div>2025-06-06</div>
            
            <a class="bn-balink" href="/zh-CN/support/announcement/detail/test3">
                <h3>币安新增JPY现货交易对并推出零挂单手续费活动</h3>
            </a>
            <div>2025-06-06</div>
        </div>
        </body>
        </html>
        """
        
        print("📊 解析模拟HTML数据...")
        announcements = parse_binance_announcements(mock_html)
        
        if announcements:
            print(f"✅ 成功解析出 {len(announcements)} 个公告")
            
            # 验证解析结果
            for i, announcement in enumerate(announcements, 1):
                print(f"  公告 {i}: {announcement.get('title', 'N/A')[:50]}...")
                
                # 检查必要字段
                required_fields = ['id', 'title', 'link']
                missing_fields = [field for field in required_fields if not announcement.get(field)]
                
                if missing_fields:
                    print(f"    ⚠️ 缺少字段: {missing_fields}")
                else:
                    print(f"    ✅ 包含所有必要字段")
            
            return True
        else:
            print("❌ 解析失败，未获取到任何公告")
            return False
            
    except Exception as e:
        print(f"❌ HTML解析测试失败: {str(e)}")
        return False

def test_screenshot_functionality():
    """测试截图功能"""
    print("📸 测试截图功能...")
    
    try:
        # 检查截图目录
        screenshots_dir = Path(config.SCREENSHOTS_DIR)
        
        if screenshots_dir.exists():
            # 检查是否有截图文件
            screenshot_files = list(screenshots_dir.glob("*.png"))
            
            if screenshot_files:
                print(f"✅ 截图目录存在，发现 {len(screenshot_files)} 个截图文件")
                
                # 显示最近的几个截图文件
                recent_files = sorted(screenshot_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
                print("📁 最近的截图文件:")
                for file in recent_files:
                    size_kb = file.stat().st_size // 1024
                    print(f"  - {file.name} ({size_kb} KB)")
                    
                return True
            else:
                print("⚠️ 截图目录存在但为空，需要运行抓取功能生成截图")
                return True
        else:
            print("⚠️ 截图目录不存在，将在首次运行时自动创建")
            return True
            
    except Exception as e:
        print(f"❌ 截图功能测试失败: {str(e)}")
        return False

async def run_comprehensive_scraper_test():
    """运行综合抓取测试"""
    print("🚀 开始综合抓取功能测试")
    print("=" * 60)
    
    test_results = {}
    
    # 1. 测试HTML解析
    print("1️⃣ HTML解析测试")
    test_results['html_parsing'] = test_html_parsing()
    print()
    
    # 2. 测试截图功能
    print("2️⃣ 截图功能测试")
    test_results['screenshot'] = test_screenshot_functionality()
    print()
    
    # 3. 测试页面获取
    print("3️⃣ 页面获取测试")
    test_results['page_fetching'] = await test_page_fetching()
    print()
    
    # 4. 测试完整抓取流程
    print("4️⃣ 完整抓取流程测试")
    test_results['full_scraping'] = test_announcement_scraping()
    print()
    
    # 总结测试结果
    print("=" * 60)
    print("📊 测试结果总结:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  - {test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有抓取功能测试通过！")
        return True
    elif passed_tests >= total_tests * 0.5:  # 至少50%测试通过
        print("⚠️ 部分测试通过，基本功能正常")
        return True
    else:
        print("❌ 大部分测试失败，需要检查配置")
        return False

def main():
    """主测试函数"""
    return asyncio.run(run_comprehensive_scraper_test())

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
