"""
Binance Alpha 監控系統配置模組
"""
import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加載環境變量
load_dotenv()

def get_clean_env(key: str, default: str = "") -> str:
    """
    獲取清理後的環境變量值（移除註釋）
    
    Args:
        key: 環境變量名
        default: 默認值
        
    Returns:
        清理後的值
    """
    value = os.getenv(key, default)
    if isinstance(value, str) and '#' in value:
        # 移除註釋部分
        value = value.split('#')[0].strip()
    return value

class Config:
    """系統配置類"""
    
    # 基礎路徑
    BASE_DIR = Path(__file__).parent
    LOGS_DIR = BASE_DIR / "logs"
    SCREENSHOTS_DIR = BASE_DIR / os.getenv("SCREENSHOTS_DIR", "screenshots")
    
    # 飛書配置
    FEISHU_WEBHOOK = os.getenv("FEISHU_WEBHOOK")
    
    # Ollama 配置
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "qwen2.5:7b")
    
    # 監控配置
    MONITOR_INTERVAL = int(get_clean_env("MONITOR_INTERVAL", "300"))  # 5分鐘
    BROWSER_HEADLESS = get_clean_env("BROWSER_HEADLESS", "true").lower() == "true"
    BROWSER_TIMEOUT = int(get_clean_env("BROWSER_TIMEOUT", "30000"))  # 30秒
    
    # 日誌配置
    LOG_LEVEL = get_clean_env("LOG_LEVEL", "INFO").upper()
    LOG_FILE = LOGS_DIR / get_clean_env("LOG_FILE", "monitor.log").replace("logs/", "")
    
    # 存儲配置
    SENT_RECORDS_FILE = BASE_DIR / os.getenv("SENT_RECORDS_FILE", "sent_records.json")
    
    # Binance URL 配置
    BASE_URL = "https://www.binance.com"
    ANNOUNCEMENT_LIST = "https://www.binance.com/zh-CN/support/announcement/list/48"
    
    # 監控目標 URL 列表
    MONITOR_TARGET_URLS = [
        "https://www.binance.com/zh-CN/support/announcement/list/48",  # Binance 公告列表
        # 可以添加更多 URL
    ]
    
    # Alpha 信息識別配置
    # 基礎 Alpha 關鍵詞列表（可配置）
    ALPHA_KEYWORDS = {
        # 核心 Alpha 關鍵詞
        "alpha_core": [
            "alpha",
            "binance alpha", 
            "new listing",
            "新幣上線",
            "new token",
            "新代幣",
            "項目上線",
            "項目啟動"
        ],
        
        # 市場相關關鍵詞
        "market_signals": [
            "spot trading",
            "現貨交易",
            "trading pairs",
            "交易對",
            "market making",
            "做市",
            "liquidity",
            "流動性",
            "airdrop",
            "空投",
            "token sale",
            "代幣銷售"
        ],
        
        # 技術和創新關鍵詞  
        "tech_innovation": [
            "defi",
            "nft",
            "web3",
            "blockchain",
            "區塊鏈",
            "智能合約",
            "smart contract",
            "layer 2",
            "layer2",
            "cross-chain",
            "跨鏈",
            "consensus",
            "共識"
        ],
        
        # 重要事件關鍵詞
        "events": [
            "mainnet",
            "主網",
            "testnet", 
            "測試網",
            "partnership",
            "合作夥伴",
            "acquisition",
            "收購",
            "funding",
            "融資",
            "investment",
            "投資",
            "staking",
            "質押"
        ]
    }
    
    # Alpha 關鍵詞權重配置
    ALPHA_KEYWORD_WEIGHTS = {
        "alpha_core": 10,      # 核心關鍵詞權重最高
        "market_signals": 7,   # 市場信號權重較高
        "tech_innovation": 5,  # 技術創新中等權重
        "events": 6            # 重要事件較高權重
    }
    
    # Alpha 信息過濾閾值
    ALPHA_SCORE_THRESHOLD = 5  # 最低分數閾值，低於此分數不視為 Alpha 信息
    
    # Alpha 識別方式配置
    ALPHA_DETECTION_METHOD = os.getenv("ALPHA_DETECTION_METHOD", "rule").lower()  # "rule", "llm", "hybrid"
    ALPHA_LLM_FALLBACK_THRESHOLD = 3  # 規則方式分數低於此值時，使用 LLM 進行二次驗證
    
    @classmethod
    def setup_directories(cls):
        """創建必要的目錄"""
        cls.LOGS_DIR.mkdir(exist_ok=True)
        cls.SCREENSHOTS_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def setup_logging(cls):
        """設置日誌配置"""
        cls.setup_directories()
        
        # 日誌格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 創建日誌配置
        logging.basicConfig(
            level=getattr(logging, cls.LOG_LEVEL),
            format=log_format,
            handlers=[
                logging.FileHandler(cls.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()  # 同時輸出到控制台
            ]
        )
        
        # 設置第三方庫的日誌級別
        logging.getLogger('playwright').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        
        return logging.getLogger(__name__)
    
    @classmethod
    def validate_config(cls):
        """驗證配置"""
        errors = []
        
        if not cls.FEISHU_WEBHOOK or cls.FEISHU_WEBHOOK == "https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_here":
            errors.append("FEISHU_WEBHOOK 未正確設置")
        
        return errors

# 初始化配置
config = Config()
logger = config.setup_logging()

# 驗證配置
config_errors = config.validate_config()
if config_errors:
    logger.warning("配置驗證發現問題:")
    for error in config_errors:
        logger.warning(f"  - {error}")
