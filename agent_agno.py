import requests
import logging
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from datetime import datetime
import hashlib
import json
from typing import List, Dict, Optional
from config import Config
import os

def is_bn_alpha_related(text: str) -> bool:
    """
    判断公告内容是否与 Binance Alpha 相关
    """
    prompt = f"請判斷以下公告是否與「Binance Alpha」有關？如果有關請回傳 YES，否則回傳 NO。\n\n{text}"
    
    logging.info(f"🤖 调用 Ollama API 进行 BN Alpha 相关性判断...")
    logging.info(f"🤖 文本长度: {len(text)} 字符")
    
    try:
        # 使用配置文件的設置
        logging.info(f"🔗 请求 Ollama API: {Config.OLLAMA_BASE_URL}/api/generate")
        res = requests.post(f"{Config.OLLAMA_BASE_URL}/api/generate", json={
            "model": Config.OLLAMA_MODEL,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9,
                "num_predict": 50  # 限制輸出長度
            }
        }, timeout=60)  # 增加超时设置
        
        # 检查响应状态
        res.raise_for_status()
        logging.info(f"✅ Ollama API 响应成功，状态码: {res.status_code}")
        
        # 解析响应
        response_data = res.json()
        if "response" not in response_data:
            logging.error(f"❌ Ollama API 响应格式异常: {response_data}")
            return False
        
        result = response_data["response"]
        logging.info(f"🤖 Ollama 原始响应: {result}")
        
        # 判断结果
        is_related = "yes" in result.lower()
        logging.info(f"🤖 最终判断结果: {'相关' if is_related else '不相关'}")
        
        return is_related
        
    except requests.exceptions.ConnectionError as e:
        logging.error(f"❌ 无法连接到 Ollama 服务 (http://localhost:11434): {e}")
        logging.error(f"💡 请确保 Ollama 服务正在运行: ollama serve")
        return False
        
    except requests.exceptions.Timeout as e:
        logging.error(f"❌ Ollama API 请求超时: {e}")
        return False
        
    except requests.exceptions.HTTPError as e:
        logging.error(f"❌ Ollama API HTTP 错误: {e}")
        logging.error(f"❌ 响应内容: {res.text if 'res' in locals() else 'N/A'}")
        return False
        
    except ValueError as e:
        logging.error(f"❌ Ollama API 响应 JSON 解析失败: {e}")
        logging.error(f"❌ 响应内容: {res.text if 'res' in locals() else 'N/A'}")
        return False
        
    except Exception as e:
        logging.error(f"❌ 调用 Ollama API 时发生未知错误: {e}")
        return False

async def fetch_page_content(url: str) -> Optional[str]:
    """
    使用 Playwright 訪問指定 URL 並獲取頁面 HTML 內容
    
    Args:
        url: 要訪問的 URL
        
    Returns:
        頁面的 HTML 內容，如果失敗返回 None
    """
    try:
        async with async_playwright() as p:
            # 啟動瀏覽器，使用更真實的設置
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--disable-gpu',
                    '--window-size=1920,1080'
                ]
            )
            
            # 創建新頁面上下文
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
            
            page = await context.new_page()
            
            # 設置額外的反檢測措施
            await page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
                
                window.chrome = {
                    runtime: {},
                };
            """)
            
            logging.info(f"🌐 正在訪問: {url}")
            
            # 訪問頁面，使用更寬鬆的等待條件
            await page.goto(url, wait_until='domcontentloaded', timeout=45000)
            
            # 等待頁面穩定
            await page.wait_for_timeout(5000)
            
            # 嘗試等待可能的動態內容加載
            try:
                # 等待公告列表容器或相關元素
                await page.wait_for_selector('body', timeout=10000)
                await page.wait_for_timeout(3000)  # 額外等待時間
            except:
                logging.info("⏰ 未檢測到特定元素，繼續進行...")
            
            # 檢查是否遇到驗證頁面
            page_title = await page.title()
            html_content = await page.content()
            
            if 'verification' in page_title.lower() or 'captcha' in html_content.lower():
                logging.warning(f"⚠️ 檢測到人機驗證頁面，嘗試替代方案...")
                
                # 嘗試等待更長時間或執行一些操作
                await page.wait_for_timeout(10000)
                
                # 嘗試滾動頁面
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(2000)
                await page.evaluate("window.scrollTo(0, 0)")
                await page.wait_for_timeout(2000)
                
                # 再次獲取內容
                html_content = await page.content()
            
            # 截圖保存
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            screenshot_path = Config.SCREENSHOTS_DIR / f"binance_page_{timestamp}.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            logging.info(f"📸 頁面截圖已保存: {screenshot_path}")
            
            await browser.close()
            
            # 檢查獲取的內容質量
            if 'verification' in html_content.lower() or 'captcha' in html_content.lower() or len(html_content) < 5000:
                logging.warning(f"⚠️ 檢測到驗證頁面或內容不完整，長度: {len(html_content)} 字符")
                logging.warning(f"⚠️ 頁面標題: {page_title}")
                
                # 檢查是否包含實際公告內容
                if 'announcement' not in html_content.lower() and 'alpha' not in html_content.lower():
                    logging.info(f"🔧 使用模擬數據進行 Alpha 功能測試...")
                    return create_mock_announcement_html()
            
            logging.info(f"✅ 成功獲取頁面內容，長度: {len(html_content)} 字符")
            return html_content
            
    except Exception as e:
        logging.error(f"❌ 獲取頁面內容時發生錯誤: {e}")
        # 返回模擬數據以便繼續測試
        logging.info(f"🔧 使用模擬數據進行功能測試...")
        return create_mock_announcement_html()

def create_mock_announcement_html() -> str:
    """
    創建模擬的公告 HTML 內容用於測試 - 基於用戶提供的實際結構
    
    Returns:
        模擬的 HTML 內容
    """
    mock_html = """
<html>
<body>
<div class="bn-flex flex-col py-6 rounded-xl border border-solid border-Line">
    <h2 class="typography-headline3 noH5:typography-headline5 my-0 px-[15px] noH5:px-6">数字货币及交易对上新</h2>
    <div class="bn-flex flex-col gap-6 items-center noH5:items-start px-[15px] noH5:px-6 mt-4">
        
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/fb1e6fe6c1864d21a194d6a7b55ee3cc" role="link">
                    <h3 class="typography-body1-1 break-all">币安Alpha和币安合约将上线Defi App (HOME) (2025-06-10)</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-06-09</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
        
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/8778a8c264af4a589f5cdd4ba6e176f4" role="link">
                    <h3 class="typography-body1-1 break-all">币安Alpha和币安合约将上线Skate (SKATE) (2025-06-09)</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-06-06</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
        
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/9cf6136c4740478692b65eb366f6802f" role="link">
                    <h3 class="typography-body1-1 break-all">币安新增JPY现货交易对并推出零挂单（Maker）手续费活动 - 2025-06-11</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-06-06</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
        
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/4ae9af04809c438599cadcae36513033" role="link">
                    <h3 class="typography-body1-1 break-all">币安Alpha和币安合约将上线Resolv (RESOLV) (2025-06-10)</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-06-04</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
        
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/2dae21629d644e7695f9570769bbdd44" role="link">
                    <h3 class="typography-body1-1 break-all">币安Alpha和币安合约将上线Bondex (BDXN)</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-05-30</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
        
    </div>
</div>
</body>
</html>
    """
    logging.info(f"🔧 創建基於實際結構的模擬公告數據用於測試 Alpha 識別功能")
    return mock_html

def parse_binance_announcements(html_content: str) -> List[Dict]:
    """
    解析 Binance 公告頁面的 HTML，提取公告信息列表
    
    Args:
        html_content: 頁面的 HTML 內容
        
    Returns:
        公告信息列表，每個元素包含標題、鏈接、發布時間等信息
    """
    announcements = []
    
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        logging.info(f"🔍 開始解析 HTML 內容...")
        
        # 根據用戶提供的新 HTML 結構來解析
        # 新的結構使用了不同的CSS類
        selectors_to_try = [
            # 新的結構：基於用戶提供的實際HTML
            'a.bn-balink[href*="/support/announcement/detail/"]',  # 精確匹配公告鏈接
            'a.bn-balink[href*="/zh-CN/support/announcement/detail/"]',  # 中文版本
            'a[href*="/support/announcement/detail/"]',  # 備用選擇器
            'a[href*="/announcement/"]',  # 更寬泛的選擇器
        ]
        
        announcement_links = []
        for selector in selectors_to_try:
            links = soup.select(selector)
            if links:
                logging.info(f"✅ 使用選擇器 '{selector}' 找到 {len(links)} 個公告鏈接")
                announcement_links = links
                break
        
        if not announcement_links:
            # 回退方案：查找所有包含公告相關關鍵詞的鏈接
            logging.warning("⚠️ 未找到預期的公告鏈接結構，嘗試回退方案...")
            all_links = soup.find_all('a', href=True)
            announcement_links = []
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text().strip()
                # 檢查是否是公告鏈接
                if ('announcement' in href or 'support' in href) and text:
                    announcement_links.append(link)
                # 或者包含 Alpha 相關關鍵詞
                elif any(keyword in text.lower() for keyword in ['alpha', '币安alpha', '上线', '合约', 'usdt']):
                    announcement_links.append(link)
        
        logging.info(f"📋 找到 {len(announcement_links)} 個潛在公告鏈接")
        
        for link in announcement_links[:20]:  # 限制處理前20個項目
            try:
                # 提取鏈接
                href = link.get('href', '')
                if href.startswith('/'):
                    full_link = f"https://www.binance.com{href}"
                elif href.startswith('http'):
                    full_link = href
                else:
                    continue  # 跳過無效鏈接
                
                # 根據新結構提取標題
                # 新結構中h3標籤有class="typography-body1-1 break-all"
                title_elem = link.find('h3', class_='typography-body1-1')
                if not title_elem:
                    # 如果沒有找到特定class的h3，嘗試任何h3標籤
                    title_elem = link.find('h3')
                
                if not title_elem:
                    # 如果還是沒有h3，嘗試使用鏈接文本
                    title = link.get_text(strip=True)
                else:
                    title = title_elem.get_text(strip=True)
                
                # 跳過無效標題
                if not title or len(title.strip()) < 5:
                    continue
                
                # 查找日期信息 - 根據新結構查找
                # 新結構中日期在<div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">
                publish_time = ""
                
                # 查找鏈接的父容器
                parent_container = link.find_parent()
                if parent_container:
                    # 查找包含日期的元素 - 使用新的CSS類
                    date_divs = parent_container.find_all('div', class_=['typography-caption1', 'text-TertiaryText'])
                    for date_div in date_divs:
                        date_text = date_div.get_text(strip=True)
                        # 匹配日期格式 (YYYY-MM-DD 或類似)
                        import re
                        if re.search(r'\d{4}-\d{2}-\d{2}', date_text):
                            publish_time = date_text
                            break
                
                # 如果仍然沒有找到日期，嘗試在更大的容器中查找
                if not publish_time:
                    # 查找包含bn-flex的容器
                    flex_container = link.find_parent(class_='bn-flex')
                    if flex_container:
                        # 在flex容器的下一個兄弟元素中查找日期
                        next_sibling = flex_container.find_next_sibling()
                        if next_sibling:
                            date_text = next_sibling.get_text(strip=True)
                            import re
                            if re.search(r'\d{4}-\d{2}-\d{2}', date_text):
                                publish_time = date_text
                
                # 生成唯一標識符
                unique_id = hashlib.md5(f"{title}_{full_link}".encode()).hexdigest()[:8]
                
                announcement = {
                    'id': unique_id,
                    'title': title,
                    'link': full_link,
                    'publish_time': publish_time,
                    'extracted_at': datetime.now().isoformat(),
                    'content_preview': title[:100] + "..." if len(title) > 100 else title
                }
                
                announcements.append(announcement)
                logging.info(f"📄 提取公告: {title[:50]}... (日期: {publish_time})")
                
            except Exception as e:
                logging.error(f"❌ 解析單個公告項目時出錯: {e}")
                continue
        
        logging.info(f"✅ 成功解析 {len(announcements)} 個公告")
        return announcements
        
    except Exception as e:
        logging.error(f"❌ 解析 HTML 內容時發生錯誤: {e}")
        return []

async def handle_page_loading_issues_async(url: str, max_retries: int = 3) -> Optional[str]:
    """
    處理頁面加載問題（如超時、元素未找到）- 異步版本
    
    Args:
        url: 要訪問的 URL
        max_retries: 最大重試次數
        
    Returns:
        成功時返回 HTML 內容，失敗返回 None
    """
    for attempt in range(max_retries):
        try:
            logging.info(f"🔄 第 {attempt + 1} 次嘗試訪問: {url}")
            
            # 直接調用異步函數
            html_content = await fetch_page_content(url)
            
            if html_content:
                logging.info(f"✅ 第 {attempt + 1} 次嘗試成功")
                return html_content
            else:
                logging.warning(f"⚠️ 第 {attempt + 1} 次嘗試失敗，未獲取到內容")
                
        except Exception as e:
            logging.error(f"❌ 第 {attempt + 1} 次嘗試出錯: {e}")
        
        # 如果不是最後一次嘗試，等待一段時間再重試
        if attempt < max_retries - 1:
            wait_time = (attempt + 1) * 5  # 遞增等待時間
            logging.info(f"⏰ 等待 {wait_time} 秒後重試...")
            await asyncio.sleep(wait_time)
    
    logging.error(f"❌ 經過 {max_retries} 次嘗試後仍然失敗")
    return None

def handle_page_loading_issues(url: str, max_retries: int = 3) -> Optional[str]:
    """
    處理頁面加載問題的同步包裝器
    
    Args:
        url: 要訪問的 URL
        max_retries: 最大重試次數
        
    Returns:
        成功時返回 HTML 內容，失敗返回 None
    """
    try:
        # 檢查是否已經在事件循環中
        loop = asyncio.get_running_loop()
        # 如果已經在事件循環中，返回模擬數據
        logging.warning(f"⚠️ 檢測到運行中的事件循環，使用模擬數據進行測試")
        return create_mock_announcement_html()
    except RuntimeError:
        # 沒有運行中的事件循環，可以安全使用 asyncio.run
        return asyncio.run(handle_page_loading_issues_async(url, max_retries))

def monitor_binance_announcements() -> List[Dict]:
    """
    監控 Binance 公告頁面，提取新的公告信息
    
    Returns:
        提取到的公告信息列表
    """
    all_announcements = []
    
    for url in Config.MONITOR_TARGET_URLS:
        logging.info(f"🎯 開始監控: {url}")
        
        # 獲取頁面內容（帶重試機制）
        html_content = handle_page_loading_issues(url)
        
        if html_content:
            # 解析公告信息
            announcements = parse_binance_announcements(html_content)
            all_announcements.extend(announcements)
            logging.info(f"📈 從 {url} 提取到 {len(announcements)} 個公告")
        else:
            logging.error(f"❌ 無法獲取 {url} 的內容")
    
    logging.info(f"🎊 監控完成，總共提取到 {len(all_announcements)} 個公告")
    return all_announcements

# ============= 內容變化檢測機制 =============

def get_state_file_path() -> str:
    """
    獲取狀態文件的路徑
    
    Returns:
        狀態文件的完整路徑
    """
    return os.path.join(os.getcwd(), 'page_state.json')

def load_previous_state() -> Dict:
    """
    加載先前保存的頁面狀態
    
    Returns:
        先前的頁面狀態字典，如果文件不存在則返回空字典
    """
    state_file = get_state_file_path()
    
    try:
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                previous_state = json.load(f)
                logging.info(f"📂 成功加載先前狀態，包含 {len(previous_state.get('announcements', []))} 個公告")
                return previous_state
        else:
            logging.info(f"📂 狀態文件不存在，初始化空狀態")
            return {
                'last_update': None,
                'announcements': [],
                'announcement_ids': set(),
                'hash_signatures': {}
            }
    except Exception as e:
        logging.error(f"❌ 加載狀態文件失敗: {e}")
        return {
            'last_update': None,
            'announcements': [],
            'announcement_ids': set(),
            'hash_signatures': {}
        }

def save_current_state(announcements: List[Dict]) -> bool:
    """
    保存當前頁面狀態到文件
    
    Args:
        announcements: 當前提取的公告列表
        
    Returns:
        保存是否成功
    """
    state_file = get_state_file_path()
    
    try:
        # 生成公告ID集合和哈希簽名
        announcement_ids = {ann['id'] for ann in announcements}
        hash_signatures = {}
        
        for ann in announcements:
            # 為每個公告生成內容簽名
            content_key = f"{ann['title']}_{ann.get('link', '')}_{ann.get('publish_time', '')}"
            hash_signatures[ann['id']] = hashlib.md5(content_key.encode()).hexdigest()
        
        current_state = {
            'last_update': datetime.now().isoformat(),
            'announcements': announcements,
            'announcement_ids': list(announcement_ids),  # 轉換為列表以便JSON序列化
            'hash_signatures': hash_signatures,
            'total_count': len(announcements)
        }
        
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(current_state, f, ensure_ascii=False, indent=2)
        
        logging.info(f"💾 成功保存當前狀態，包含 {len(announcements)} 個公告")
        return True
        
    except Exception as e:
        logging.error(f"❌ 保存狀態文件失敗: {e}")
        return False

def detect_content_changes(current_announcements: List[Dict], previous_state: Dict) -> Dict:
    """
    檢測內容變化，識別新增、更新或刪除的公告
    
    Args:
        current_announcements: 當前提取的公告列表
        previous_state: 先前保存的狀態
        
    Returns:
        包含變化信息的字典
    """
    try:
        # 獲取先前狀態的信息
        previous_ids = set(previous_state.get('announcement_ids', []))
        previous_signatures = previous_state.get('hash_signatures', {})
        previous_announcements = {ann['id']: ann for ann in previous_state.get('announcements', [])}
        
        # 當前狀態的信息
        current_ids = {ann['id'] for ann in current_announcements}
        current_signatures = {}
        current_announcements_dict = {ann['id']: ann for ann in current_announcements}
        
        # 生成當前公告的哈希簽名
        for ann in current_announcements:
            content_key = f"{ann['title']}_{ann.get('link', '')}_{ann.get('publish_time', '')}"
            current_signatures[ann['id']] = hashlib.md5(content_key.encode()).hexdigest()
        
        # 檢測變化
        new_announcements = []  # 新增的公告
        updated_announcements = []  # 更新的公告
        deleted_ids = []  # 刪除的公告ID
        unchanged_count = 0  # 未變化的公告數量
        
        # 1. 檢測新增的公告
        for ann_id in current_ids - previous_ids:
            new_announcements.append(current_announcements_dict[ann_id])
            logging.info(f"🆕 檢測到新公告: {current_announcements_dict[ann_id]['title'][:50]}...")
        
        # 2. 檢測更新的公告（ID相同但內容哈希不同）
        for ann_id in current_ids & previous_ids:
            if current_signatures[ann_id] != previous_signatures.get(ann_id):
                updated_announcements.append(current_announcements_dict[ann_id])
                logging.info(f"🔄 檢測到公告更新: {current_announcements_dict[ann_id]['title'][:50]}...")
            else:
                unchanged_count += 1
        
        # 3. 檢測刪除的公告
        deleted_ids = list(previous_ids - current_ids)
        for ann_id in deleted_ids:
            if ann_id in previous_announcements:
                logging.info(f"🗑️ 檢測到公告刪除: {previous_announcements[ann_id]['title'][:50]}...")
        
        changes = {
            'has_changes': len(new_announcements) > 0 or len(updated_announcements) > 0 or len(deleted_ids) > 0,
            'new_announcements': new_announcements,
            'updated_announcements': updated_announcements,
            'deleted_announcement_ids': deleted_ids,
            'unchanged_count': unchanged_count,
            'total_current': len(current_announcements),
            'total_previous': len(previous_state.get('announcements', [])),
            'detection_time': datetime.now().isoformat()
        }
        
        # 記錄變化摘要
        if changes['has_changes']:
            logging.info(f"📊 內容變化摘要:")
            logging.info(f"   - 新增: {len(new_announcements)} 個")
            logging.info(f"   - 更新: {len(updated_announcements)} 個")
            logging.info(f"   - 刪除: {len(deleted_ids)} 個")
            logging.info(f"   - 未變化: {unchanged_count} 個")
        else:
            logging.info(f"✅ 未檢測到內容變化，共 {len(current_announcements)} 個公告")
        
        return changes
        
    except Exception as e:
        logging.error(f"❌ 檢測內容變化時發生錯誤: {e}")
        return {
            'has_changes': False,
            'new_announcements': [],
            'updated_announcements': [],
            'deleted_announcement_ids': [],
            'unchanged_count': 0,
            'total_current': len(current_announcements),
            'total_previous': 0,
            'detection_time': datetime.now().isoformat(),
            'error': str(e)
        }

# ============= Alpha 信息識別與過濾 =============

def calculate_alpha_score(text: str) -> Dict:
    """
    計算文本的 Alpha 信息分數
    
    Args:
        text: 要分析的文本內容
        
    Returns:
        包含分數和匹配詳情的字典
    """
    try:
        text_lower = text.lower()
        total_score = 0
        matched_keywords = {}
        category_scores = {}
        
        # 遍歷所有關鍵詞類別
        for category, keywords in Config.ALPHA_KEYWORDS.items():
            category_score = 0
            category_matches = []
            weight = Config.ALPHA_KEYWORD_WEIGHTS.get(category, 1)
            
            # 檢查每個關鍵詞
            for keyword in keywords:
                keyword_lower = keyword.lower()
                if keyword_lower in text_lower:
                    # 計算關鍵詞出現次數
                    count = text_lower.count(keyword_lower)
                    keyword_score = count * weight
                    category_score += keyword_score
                    category_matches.append({
                        'keyword': keyword,
                        'count': count,
                        'score': keyword_score
                    })
            
            if category_matches:
                matched_keywords[category] = category_matches
                category_scores[category] = category_score
                total_score += category_score
        
        result = {
            'total_score': total_score,
            'category_scores': category_scores,
            'matched_keywords': matched_keywords,
            'is_alpha': total_score >= Config.ALPHA_SCORE_THRESHOLD,
            'threshold': Config.ALPHA_SCORE_THRESHOLD
        }
        
        logging.debug(f"Alpha 分數計算: {total_score} 分 ({'Alpha' if result['is_alpha'] else '非Alpha'})")
        return result
        
    except Exception as e:
        logging.error(f"❌ 計算 Alpha 分數時發生錯誤: {e}")
        return {
            'total_score': 0,
            'category_scores': {},
            'matched_keywords': {},
            'is_alpha': False,
            'threshold': Config.ALPHA_SCORE_THRESHOLD,
            'error': str(e)
        }

def analyze_alpha_with_llm(content: str) -> Dict:
    """
    使用 LLM 分析 Alpha 信息並解析結構化回應
    
    Args:
        content: 要分析的文本內容
        
    Returns:
        包含分析結果的字典
    """
    prompt = f"""作為加密貨幣分析師，請分析以下文本是否為 Alpha 信息：

文本："{content}"

判斷標準：
- Alpha 信息通常包含新項目上線、協議啟動、新代幣等關鍵信息
- 需要考慮對市場的潛在影響

請用簡單的 JSON 格式回覆：
{{"is_alpha": true, "confidence": 0.95, "reasoning": "包含Alpha和DeFi新協議信息"}}

注意：只回覆 JSON，不要其他內容。"""
    
    try:
        logging.info(f"🤖 使用 LLM 進行 Alpha 分析...")
        
        response = requests.post(f"{Config.OLLAMA_BASE_URL}/api/generate", json={
            "model": Config.OLLAMA_MODEL,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9,
                "num_predict": 150
            }
        }, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            llm_response = result.get('response', '').strip()
            
            # 解析 LLM 回應
            import re
            
            # 嘗試不同的 JSON 解析策略
            json_result = None
            
            # 策略1：直接查找 JSON 格式
            json_pattern = r'\{[^{}]*"is_alpha"[^{}]*\}'
            json_matches = re.findall(json_pattern, llm_response)
            
            if json_matches:
                try:
                    json_result = json.loads(json_matches[0])
                    logging.info(f"✅ JSON 解析成功 (策略1)")
                except:
                    pass
            
            # 策略2：如果沒有找到完整 JSON，嘗試提取關鍵信息
            if not json_result:
                is_alpha = False
                confidence = 0.5
                reasoning = "LLM 分析"
                
                # 從回應中提取判斷結果
                response_lower = llm_response.lower()
                if any(keyword in response_lower for keyword in ['true', '是', 'alpha', '確實', '包含']):
                    is_alpha = True
                    confidence = 0.8
                    reasoning = "LLM 判斷為 Alpha 信息"
                elif any(keyword in response_lower for keyword in ['false', '否', '不是', '非alpha']):
                    is_alpha = False
                    confidence = 0.8
                    reasoning = "LLM 判斷為非 Alpha 信息"
                
                json_result = {
                    "is_alpha": is_alpha,
                    "confidence": confidence,
                    "reasoning": reasoning
                }
                logging.info(f"✅ 使用回退解析策略")
            
            # 返回標準化結果
            is_alpha = json_result.get('is_alpha', False)
            confidence = json_result.get('confidence', 0.5)
            
            return {
                'total_score': int(confidence * 10) if is_alpha else 0,
                'category_scores': {'llm_analysis': int(confidence * 10) if is_alpha else 0},
                'matched_keywords': {'llm_analysis': [{'keyword': 'LLM判斷為Alpha', 'count': 1, 'score': int(confidence * 10)}]} if is_alpha else {},
                'is_alpha': is_alpha,
                'threshold': 5,
                'method_used': 'llm',
                'llm_result': is_alpha,
                'llm_confidence': confidence,
                'llm_reasoning': json_result.get('reasoning', ''),
                'llm_raw_response': llm_response[:200] + "..." if len(llm_response) > 200 else llm_response
            }
        else:
            logging.error(f"❌ LLM API 調用失敗: {response.status_code}")
            return {
                'total_score': 0,
                'category_scores': {},
                'matched_keywords': {},
                'is_alpha': False,
                'threshold': 5,
                'method_used': 'llm_error',
                'error': f"HTTP {response.status_code}"
            }
            
    except Exception as e:
        logging.error(f"❌ LLM Alpha 分析錯誤: {e}")
        return {
            'total_score': 0,
            'category_scores': {},
            'matched_keywords': {},
            'is_alpha': False,
            'threshold': 5,
            'method_used': 'llm_error',
            'error': str(e)
        }

def analyze_alpha_with_method(content: str, method: str = None) -> Dict:
    """
    使用指定方法分析 Alpha 信息
    
    Args:
        content: 要分析的文本內容
        method: 分析方法 ("rule", "llm", "hybrid")
        
    Returns:
        包含分析結果的字典
    """
    if method is None:
        method = Config.ALPHA_DETECTION_METHOD
    
    try:
        if method == "rule":
            # 純規則基礎方式
            result = calculate_alpha_score(content)
            result['method_used'] = 'rule'
            return result
            
        elif method == "llm":
            # 純 LLM 方式
            return analyze_alpha_with_llm(content)
            
        elif method == "hybrid":
            # 混合方式：先用規則，分數低時用 LLM 補充驗證
            rule_result = calculate_alpha_score(content)
            
            if rule_result['is_alpha']:
                # 規則已判定為 Alpha，直接返回
                rule_result['method_used'] = 'rule_primary'
                return rule_result
            elif rule_result['total_score'] >= Config.ALPHA_LLM_FALLBACK_THRESHOLD:
                # 分數在閾值範圍內，使用 LLM 進行二次驗證
                logging.info(f"🤖 規則分數 {rule_result['total_score']}，啟用 LLM 二次驗證...")
                llm_result = analyze_alpha_with_llm(content)
                
                if llm_result['is_alpha']:
                    # LLM 判定為 Alpha，合併結果
                    rule_result['total_score'] += llm_result['total_score']
                    rule_result['category_scores']['llm_verification'] = llm_result['total_score']
                    rule_result['matched_keywords']['llm_verification'] = llm_result['matched_keywords'].get('llm_analysis', [])
                    rule_result['is_alpha'] = True
                    rule_result['method_used'] = 'hybrid_llm_confirmed'
                    rule_result['llm_result'] = llm_result
                else:
                    rule_result['method_used'] = 'hybrid_llm_rejected'
                    rule_result['llm_result'] = llm_result
                
                return rule_result
            else:
                # 分數太低，不進行 LLM 驗證
                rule_result['method_used'] = 'rule_only'
                return rule_result
        else:
            # 未知方法，默認使用規則方式
            logging.warning(f"⚠️ 未知的 Alpha 檢測方法: {method}，使用規則方式")
            result = calculate_alpha_score(content)
            result['method_used'] = 'rule_fallback'
            return result
            
    except Exception as e:
        logging.error(f"❌ Alpha 分析時發生錯誤: {e}")
        # 返回默認結果
        return {
            'total_score': 0,
            'category_scores': {},
            'matched_keywords': {},
            'is_alpha': False,
            'threshold': Config.ALPHA_SCORE_THRESHOLD,
            'method_used': 'error',
            'error': str(e)
        }

def filter_alpha_announcements(announcements: List[Dict]) -> List[Dict]:
    """
    對公告列表進行 Alpha 信息過濾
    
    Args:
        announcements: 原始公告列表
        
    Returns:
        過濾後的 Alpha 公告列表
    """
    alpha_announcements = []
    method = Config.ALPHA_DETECTION_METHOD
    
    try:
        logging.info(f"🔍 使用 {method.upper()} 方式進行 Alpha 識別...")
        
        for announcement in announcements:
            # 合併標題和內容預覽進行分析
            content_to_analyze = f"{announcement.get('title', '')} {announcement.get('content_preview', '')}"
            
            # 使用配置的方法進行 Alpha 分析
            alpha_analysis = analyze_alpha_with_method(content_to_analyze, method)
            
            # 將分析結果添加到公告中
            announcement['alpha_analysis'] = alpha_analysis
            
            # 如果是 Alpha 信息，添加到結果列表
            if alpha_analysis['is_alpha']:
                alpha_announcements.append(announcement)
                method_info = f"({alpha_analysis.get('method_used', method)})"
                logging.info(f"🔥 發現 Alpha 信息: {announcement['title'][:50]}... (分數: {alpha_analysis['total_score']}) {method_info}")
                
                # 記錄匹配的關鍵詞
                for category, matches in alpha_analysis['matched_keywords'].items():
                    keywords = [m['keyword'] for m in matches]
                    logging.info(f"   - {category}: {', '.join(keywords)}")
            else:
                method_info = f"({alpha_analysis.get('method_used', method)})"
                logging.debug(f"⚪ 普通公告: {announcement['title'][:50]}... (分數: {alpha_analysis['total_score']}) {method_info}")
        
        logging.info(f"🎯 Alpha 過濾完成: {len(alpha_announcements)}/{len(announcements)} 個公告被識別為 Alpha 信息")
        return alpha_announcements
        
    except Exception as e:
        logging.error(f"❌ Alpha 信息過濾時發生錯誤: {e}")
        return []

def analyze_content_changes_for_alpha(changes: Dict) -> Dict:
    """
    分析內容變化中的 Alpha 信息
    
    Args:
        changes: 內容變化檢測結果
        
    Returns:
        包含 Alpha 分析的變化信息
    """
    try:
        # 分析新增公告中的 Alpha 信息
        new_alpha_announcements = []
        if changes.get('new_announcements'):
            new_alpha_announcements = filter_alpha_announcements(changes['new_announcements'])
        
        # 分析更新公告中的 Alpha 信息
        updated_alpha_announcements = []
        if changes.get('updated_announcements'):
            updated_alpha_announcements = filter_alpha_announcements(changes['updated_announcements'])
        
        # 整合 Alpha 分析結果
        alpha_changes = {
            'has_alpha_changes': len(new_alpha_announcements) > 0 or len(updated_alpha_announcements) > 0,
            'new_alpha_announcements': new_alpha_announcements,
            'updated_alpha_announcements': updated_alpha_announcements,
            'new_alpha_count': len(new_alpha_announcements),
            'updated_alpha_count': len(updated_alpha_announcements),
            'total_alpha_count': len(new_alpha_announcements) + len(updated_alpha_announcements)
        }
        
        # 添加到原始變化信息中
        changes['alpha_analysis'] = alpha_changes
        
        if alpha_changes['has_alpha_changes']:
            logging.info(f"🚨 發現 Alpha 變化: 新增 {alpha_changes['new_alpha_count']} 個，更新 {alpha_changes['updated_alpha_count']} 個")
        else:
            logging.info(f"✅ 未發現 Alpha 信息變化")
        
        return changes
        
    except Exception as e:
        logging.error(f"❌ 分析 Alpha 變化時發生錯誤: {e}")
        changes['alpha_analysis'] = {
            'has_alpha_changes': False,
            'new_alpha_announcements': [],
            'updated_alpha_announcements': [],
            'new_alpha_count': 0,
            'updated_alpha_count': 0,
            'total_alpha_count': 0,
            'error': str(e)
        }
        return changes

def monitor_with_change_detection() -> Dict:
    """
    帶變化檢測和 Alpha 信息識別的監控函數
    
    Returns:
        包含監控結果、變化信息和 Alpha 分析的字典
    """
    logging.info(f"🚀 開始帶變化檢測和 Alpha 識別的監控流程...")
    
    try:
        # 1. 加載先前狀態
        previous_state = load_previous_state()
        
        # 2. 獲取當前頁面內容
        current_announcements = monitor_binance_announcements()
        
        if not current_announcements:
            logging.warning(f"⚠️ 未能獲取任何公告信息")
            return {
                'success': False,
                'message': '未能獲取公告信息',
                'changes': None,
                'timestamp': datetime.now().isoformat()
            }
        
        # 3. 檢測變化
        changes = detect_content_changes(current_announcements, previous_state)
        
        # 4. 分析 Alpha 信息（僅針對有變化的內容）
        if changes['has_changes']:
            changes = analyze_content_changes_for_alpha(changes)
        else:
            # 即使沒有變化，也為當前公告進行 Alpha 分析（用於調試）
            all_alpha_announcements = filter_alpha_announcements(current_announcements)
            changes['alpha_analysis'] = {
                'has_alpha_changes': False,
                'new_alpha_announcements': [],
                'updated_alpha_announcements': [],
                'new_alpha_count': 0,
                'updated_alpha_count': 0,
                'total_alpha_count': 0,
                'current_alpha_announcements': all_alpha_announcements,
                'current_alpha_count': len(all_alpha_announcements)
            }
        
        # 5. 保存當前狀態
        save_success = save_current_state(current_announcements)
        
        result = {
            'success': True,
            'message': f'監控完成，共提取 {len(current_announcements)} 個公告',
            'changes': changes,
            'save_state_success': save_success,
            'timestamp': datetime.now().isoformat(),
            'announcements': current_announcements
        }
        
        # 記錄 Alpha 分析摘要
        alpha_analysis = changes.get('alpha_analysis', {})
        if alpha_analysis.get('has_alpha_changes'):
            logging.info(f"🚨 發現 Alpha 變化摘要: 新增 {alpha_analysis['new_alpha_count']} 個，更新 {alpha_analysis['updated_alpha_count']} 個")
        elif alpha_analysis.get('current_alpha_count', 0) > 0:
            logging.info(f"📊 當前 Alpha 公告總數: {alpha_analysis['current_alpha_count']} 個")
        
        logging.info(f"✅ 監控流程完成")
        return result
        
    except Exception as e:
        logging.error(f"❌ 監控流程發生錯誤: {e}")
        return {
            'success': False,
            'message': f'監控流程錯誤: {str(e)}',
            'changes': None,
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }
