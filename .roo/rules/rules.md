
Documentation Maintenance

1. Reflective Documentation
   - Keep comprehensive, accurate, and logically structured documentation updated through symbolic reasoning.
   - Document test coverage and testing strategies.

2. Continuous Updates
   - Regularly revisit and refine guidelines to reflect evolving practices and accumulated project knowledge.
   - Update test documentation as requirements change.

3. Check each file once
   - Ensure all files are checked for accuracy and relevance.
   - Verify test files match implementation files.

4. Use of Comments
   - Use comments to clarify complex logic and provide context for future developers.
   - Include comments in test files explaining test strategy and coverage.