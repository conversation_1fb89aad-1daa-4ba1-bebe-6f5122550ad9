# 🚀 Binance Alpha 监控系统 - 快速启动指南

## 📋 当前状态
✅ **Docker 镜像已构建完成**  
✅ **容器正在运行中**  
✅ **Playwright 网页截取功能正常**  
✅ **飞书通知已测试成功**  

## 🎯 快速操作命令

### 基本操作
```bash
# 查看服务状态
./docker-run.sh status

# 查看实时日志
./docker-run.sh logs

# 重启服务
./docker-run.sh restart

# 停止服务
./docker-run.sh stop

# 启动服务
./docker-run.sh start
```

### 直接使用 Docker Compose
```bash
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f bn_alpha_monitor

# 重启容器
docker-compose restart

# 停止服务
docker-compose down

# 启动服务
docker-compose up -d
```

## 📊 监控功能

您的系统现在会：
- ⏰ **每5分钟**自动检查 Binance Alpha 公告
- 🌐 **使用 Playwright** 截取网页内容
- 🤖 **AI 分析**新公告的重要性
- 📱 **飞书通知**重要更新
- 🧹 **每日清理**旧数据

## 📁 数据存储

所有数据都持久化存储在：
- `./logs/` - 系统日志
- `./data/` - 监控数据
- `./screenshots/` - 网页截图

## 🔧 配置修改

如需修改配置，编辑 `.env` 文件：
```bash
# 飞书 Webhook（必需）
FEISHU_WEBHOOK=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook

# Ollama 配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:3b

# 监控间隔（秒）
MONITOR_INTERVAL=300
```

修改后重启服务：
```bash
./docker-run.sh restart
```

## 🏥 健康检查

系统内置健康检查，会自动监控：
- 📝 日志文件更新状态
- 📂 必要目录完整性
- 📋 发送记录文件状态

## 🆘 故障排除

### 容器无法启动
```bash
# 查看详细日志
docker-compose logs bn_alpha_monitor

# 重新构建镜像
./docker-run.sh build
```

### 网页截取失败
```bash
# 检查 Playwright 状态
docker exec bn_alpha_monitor playwright --version

# 重新安装浏览器
docker-compose down
./docker-run.sh build
./docker-run.sh start
```

### 飞书通知失败
```bash
# 检查 Webhook 配置
grep FEISHU_WEBHOOK .env

# 测试网络连接
docker exec bn_alpha_monitor curl -I https://open.feishu.cn
```

## 📈 生产环境部署

使用生产配置：
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🎉 恭喜！

您的 Binance Alpha 监控系统现在已经：
- 🐳 **完全容器化**
- 🔄 **自动运行**
- 📱 **实时通知**
- 🛡️ **健康监控**

系统将持续监控 Binance Alpha 公告，并在发现重要更新时通过飞书通知您！
