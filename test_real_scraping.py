#!/usr/bin/env python3
"""
独立的真实网页抓取测试脚本
使用 Playwright 直接抓取币安公告页面
"""
import asyncio
import json
from datetime import datetime
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 导入必要的模块
from agent_agno import fetch_page_content, parse_binance_announcements, filter_alpha_announcements
from config import Config

async def test_real_binance_scraping():
    """测试真实的币安公告抓取"""
    print("🚀 开始真实的币安公告抓取测试")
    print("=" * 60)
    
    target_url = "https://www.binance.com/zh-CN/support/announcement/list/48"
    
    try:
        print(f"🌐 正在访问: {target_url}")
        
        # 1. 使用 Playwright 获取页面内容
        html_content = await fetch_page_content(target_url)
        
        if not html_content:
            print("❌ 未能获取页面内容")
            return False
            
        print(f"✅ 成功获取页面内容，长度: {len(html_content)} 字符")
        
        # 2. 解析公告信息
        print("\n📋 解析公告信息...")
        announcements = parse_binance_announcements(html_content)
        
        if not announcements:
            print("❌ 未能解析出任何公告")
            return False
            
        print(f"✅ 成功解析出 {len(announcements)} 个公告")
        
        # 3. 显示前5个公告的详细信息
        print("\n📊 公告详细信息:")
        for i, announcement in enumerate(announcements[:5], 1):
            print(f"\n公告 {i}:")
            print(f"  - ID: {announcement.get('id', 'N/A')}")
            print(f"  - 标题: {announcement.get('title', 'N/A')}")
            print(f"  - 链接: {announcement.get('link', 'N/A')}")
            print(f"  - 发布时间: {announcement.get('publish_time', 'N/A')}")
            print(f"  - 提取时间: {announcement.get('extracted_at', 'N/A')}")
        
        if len(announcements) > 5:
            print(f"\n... 还有 {len(announcements) - 5} 个公告")
        
        # 4. Alpha 信息分析
        print(f"\n🔍 分析 Alpha 信息...")
        alpha_announcements = filter_alpha_announcements(announcements)
        
        print(f"🎯 发现 {len(alpha_announcements)} 个 Alpha 相关公告:")
        for i, announcement in enumerate(alpha_announcements, 1):
            analysis = announcement.get('alpha_analysis', {})
            print(f"\nAlpha公告 {i}:")
            print(f"  - 标题: {announcement['title']}")
            print(f"  - 分数: {analysis.get('total_score', 0)}")
            print(f"  - 方法: {analysis.get('method_used', 'unknown')}")
            
            # 显示匹配的关键词
            matched_keywords = analysis.get('matched_keywords', {})
            if matched_keywords:
                print(f"  - 匹配关键词:")
                for category, matches in matched_keywords.items():
                    keywords = [m['keyword'] for m in matches]
                    print(f"    * {category}: {', '.join(keywords)}")
        
        # 5. 保存结果到文件
        results = {
            'timestamp': datetime.now().isoformat(),
            'url': target_url,
            'total_announcements': len(announcements),
            'alpha_announcements_count': len(alpha_announcements),
            'announcements': announcements,
            'alpha_announcements': alpha_announcements
        }
        
        output_file = Path('real_scraping_results.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {output_file}")
        
        # 6. 测试总结
        print(f"\n📊 抓取测试总结:")
        print(f"  - 目标URL: {target_url}")
        print(f"  - 页面内容长度: {len(html_content)} 字符")
        print(f"  - 总公告数: {len(announcements)}")
        print(f"  - Alpha公告数: {len(alpha_announcements)}")
        print(f"  - Alpha比例: {len(alpha_announcements)/len(announcements)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 抓取测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_individual_announcement_parsing():
    """测试单个公告的解析功能"""
    print("\n🔍 测试单个公告解析功能")
    print("=" * 60)
    
    # 使用用户提供的实际HTML结构进行测试
    sample_html = """
<div class="bn-flex flex-col py-6 rounded-xl border border-solid border-Line">
    <h2 class="typography-headline3 noH5:typography-headline5 my-0 px-[15px] noH5:px-6">数字货币及交易对上新</h2>
    <div class="bn-flex flex-col gap-6 items-center noH5:items-start px-[15px] noH5:px-6 mt-4">
        <div class="bn-flex w-full flex-col gap-4">
            <div class="bn-flex flex-col gap-1 noH5:gap-2">
                <a class="bn-balink text-PrimaryText hover:text-PrimaryYellow active:text-PrimaryYellow focus:text-PrimaryYellow cursor-pointer no-underline w-fit" href="/zh-CN/support/announcement/detail/fb1e6fe6c1864d21a194d6a7b55ee3cc" role="link">
                    <h3 class="typography-body1-1 break-all">币安Alpha和币安合约将上线Defi App (HOME) (2025-06-10)</h3>
                </a>
                <div class="typography-caption1 noH5:typography-body1-1 text-TertiaryText mobile:text-SecondaryText">2025-06-09</div>
            </div>
            <div role="separator" class="bn-divider"></div>
        </div>
    </div>
</div>
    """
    
    try:
        announcements = parse_binance_announcements(sample_html)
        
        if announcements:
            print(f"✅ 成功解析出 {len(announcements)} 个公告")
            for announcement in announcements:
                print(f"  - 标题: {announcement['title']}")
                print(f"  - 链接: {announcement['link']}")
                print(f"  - 日期: {announcement['publish_time']}")
        else:
            print("❌ 未能解析出任何公告")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 解析测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 币安公告真实抓取测试工具")
    print("=" * 80)
    
    test_results = {}
    
    # 1. 测试单个公告解析
    print("1️⃣ 单个公告解析测试")
    test_results['parsing'] = await test_individual_announcement_parsing()
    
    # 2. 测试真实网页抓取
    print("\n2️⃣ 真实网页抓取测试")
    test_results['real_scraping'] = await test_real_binance_scraping()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  - {test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有真实抓取测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查网络连接或页面结构变化")
        return False

if __name__ == "__main__":
    # 运行真实抓取测试
    success = asyncio.run(main())
    exit(0 if success else 1)
